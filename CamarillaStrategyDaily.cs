#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.Indicators;
using NinjaTrader.NinjaScript.DrawingTools;
#endregion

//This namespace holds Strategies in this folder and is required. Do not change it. 
namespace NinjaTrader.NinjaScript.Strategies
{
    public class CamarillaStrategy : Strategy
    {
        private EMA ema;
        private double h4;
        private double l4;
        private double entryPrice;
        private double highestSinceEntry;
        private double lowestSinceEntry;
        private bool longTrailingActive;
        private bool shortTrailingActive;
        private bool isInLongPosition;
        private bool isInShortPosition;

        protected override void OnStateChange()
        {
            if (State == State.SetDefaults)
            {
                Name = "CamarillaStrategy";
                Description = "Camarilla Strategy based on H4/L4 levels with custom trailing stop";
                Calculate = Calculate.OnEachTick; // Always use OnEachTick to match TradingView behavior
                IsOverlay = true;
                
                // Default parameters - matching TradingView pine script
                EmaPeriod = 8;
                LongTrailPoints = 60;
                LongTrailOffset = 1;
                LongStopLoss = 100;
                ShortTrailPoints = 40;
                ShortTrailOffset = 1;
                ShortStopLoss = 70;
                
                // Initialize variables
                isInLongPosition = false;
                isInShortPosition = false;
                longTrailingActive = false;
                shortTrailingActive = false;
            }
            else if (State == State.DataLoaded)
            {
                ema = EMA(EmaPeriod);
            }
        }

        protected override void OnBarUpdate()
        {
            if (CurrentBar < 5)
                return;

            // Calculate Camarilla levels using previous day's data
            // This is the exact formula from the pine script
            double prevHigh = High[1];
            double prevLow = Low[1];
            double prevClose = Close[1];
            
            // Calculate H4 and L4 exactly as in pine script
            h4 = prevClose + (prevHigh - prevLow) * 1.1 / 2.0;
            l4 = prevClose - (prevHigh - prevLow) * 1.1 / 2.0;
            
            // Debug output to verify calculations
            if (IsFirstTickOfBar)
            {
                Print($"Date: {Time[0]}, Prev High: {prevHigh}, Prev Low: {prevLow}, Prev Close: {prevClose}");
                Print($"H4: {h4}, L4: {l4}, Current Close: {Close[0]}, Current Open: {Open[0]}, EMA: {ema[0]}");
            }

            // Entry signals only on bar close (like in TradingView)
            if (Calculate == Calculate.OnBarClose || IsFirstTickOfBar)
            {
                // Long entry condition - matching TradingView pine script exactly
                if (Close[0] > h4 && Open[0] < h4 && ema[0] < Close[0] && Position.MarketPosition == MarketPosition.Flat)
                {
                    EnterLong(1, "Long");
                    entryPrice = Close[0];
                    highestSinceEntry = Close[0];
                    isInLongPosition = true;
                    isInShortPosition = false;
                    longTrailingActive = false;
                    
                    // Log entry
                    Print($"LONG ENTRY: {Instrument.FullName} at {Close[0]}, Time: {Time[0]}");
                    Print($"Entry Conditions: Close({Close[0]}) > H4({h4}), Open({Open[0]}) < H4({h4}), EMA({ema[0]}) < Close({Close[0]})");
                }
                // Short entry condition - matching TradingView pine script exactly
                else if (Close[0] < l4 && Open[0] > l4 && ema[0] > Close[0] && Position.MarketPosition == MarketPosition.Flat)
                {
                    EnterShort(1, "Short");
                    entryPrice = Close[0];
                    lowestSinceEntry = Close[0];
                    isInShortPosition = true;
                    isInLongPosition = false;
                    shortTrailingActive = false;
                    
                    // Log entry
                    Print($"SHORT ENTRY: {Instrument.FullName} at {Close[0]}, Time: {Time[0]}");
                    Print($"Entry Conditions: Close({Close[0]}) < L4({l4}), Open({Open[0]}) > L4({l4}), EMA({ema[0]}) > Close({Close[0]})");
                }
            }

            // Trailing stop logic runs on each tick (like in TradingView)
            // Manage trailing stop for long
            if (Position.MarketPosition == MarketPosition.Long && isInLongPosition)
            {
                if (Close[0] > highestSinceEntry)
                    highestSinceEntry = Close[0];

                // Activate trailing stop only after price moves LongTrailOffset ticks in our favor
                // This exactly matches TradingView's trail_offset parameter
                if (!longTrailingActive && Close[0] >= entryPrice + (LongTrailOffset * TickSize))
                {
                    longTrailingActive = true;
                    Print($"LONG TRAIL ACTIVATED: Price={Close[0]}, Entry={entryPrice}, Offset={LongTrailOffset} ticks");
                }

                if (longTrailingActive)
                {
                    double newStop = highestSinceEntry - (LongTrailPoints * TickSize);
                    ExitLongStopMarket(0, true, Position.Quantity, newStop, "ExitLongTrail", "Long");
                }

                // Fixed stop loss
                if (Close[0] <= entryPrice - (LongStopLoss * TickSize))
                {
                    ExitLong("ExitLongStop", "Long");
                    Print($"LONG STOP LOSS: Price={Close[0]}, Entry={entryPrice}, Loss={LongStopLoss} ticks");
                    isInLongPosition = false;
                }
            }

            // Manage trailing stop for short
            if (Position.MarketPosition == MarketPosition.Short && isInShortPosition)
            {
                if (Close[0] < lowestSinceEntry)
                    lowestSinceEntry = Close[0];

                // Activate trailing stop only after price moves ShortTrailOffset ticks in our favor
                // This exactly matches TradingView's trail_offset parameter
                if (!shortTrailingActive && Close[0] <= entryPrice - (ShortTrailOffset * TickSize))
                {
                    shortTrailingActive = true;
                    Print($"SHORT TRAIL ACTIVATED: Price={Close[0]}, Entry={entryPrice}, Offset={ShortTrailOffset} ticks");
                }

                if (shortTrailingActive)
                {
                    double newStop = lowestSinceEntry + (ShortTrailPoints * TickSize);
                    ExitShortStopMarket(0, true, Position.Quantity, newStop, "ExitShortTrail", "Short");
                }

                // Fixed stop loss
                if (Close[0] >= entryPrice + (ShortStopLoss * TickSize))
                {
                    ExitShort("ExitShortStop", "Short");
                    Print($"SHORT STOP LOSS: Price={Close[0]}, Entry={entryPrice}, Loss={ShortStopLoss} ticks");
                    isInShortPosition = false;
                }
            }
            
            // Reset position flags if we're flat
            if (Position.MarketPosition == MarketPosition.Flat)
            {
                isInLongPosition = false;
                isInShortPosition = false;
            }
        }
        
        #region Properties
        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "EMA Period", Description = "Period for the EMA indicator", Order = 1, GroupName = "Parameters")]
        public int EmaPeriod { get; set; }
        
        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "Long Trail Points", Description = "Trail points for long positions", Order = 2, GroupName = "Parameters")]
        public int LongTrailPoints { get; set; }
        
        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "Long Trail Offset", Description = "Trail offset for long positions", Order = 3, GroupName = "Parameters")]
        public int LongTrailOffset { get; set; }
        
        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "Long Stop Loss", Description = "Stop loss for long positions", Order = 4, GroupName = "Parameters")]
        public int LongStopLoss { get; set; }
        
        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "Short Trail Points", Description = "Trail points for short positions", Order = 5, GroupName = "Parameters")]
        public int ShortTrailPoints { get; set; }
        
        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "Short Trail Offset", Description = "Trail offset for short positions", Order = 6, GroupName = "Parameters")]
        public int ShortTrailOffset { get; set; }
        
        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "Short Stop Loss", Description = "Stop loss for short positions", Order = 7, GroupName = "Parameters")]
        public int ShortStopLoss { get; set; }
        #endregion
    }
}
