#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.Indicators;
using NinjaTrader.NinjaScript.DrawingTools;
#endregion

//This namespace holds Strategies in this folder and is required. Do not change it. 
namespace NinjaTrader.NinjaScript.Strategies
{
    public class CamarillaStrategy : Strategy
    {
        private EMA ema;
        private double h4;
        private double l4;
        private double entryPrice;
        private double highestSinceEntry;
        private double lowestSinceEntry;
        private bool longTrailingActive;
        private bool shortTrailingActive;
        private bool isInLongPosition;
        private bool isInShortPosition;
        private double pointSize; // Size of 1 point in ticks

        protected override void OnStateChange()
        {
            if (State == State.SetDefaults)
            {
                Name = "CamarillaStrategy";
                Description = "Camarilla Strategy based on H4/L4 levels with custom trailing stop";
                Calculate = Calculate.OnEachTick; // Always use OnEachTick to match TradingView behavior
                IsOverlay = true;
                
                // Default parameters - matching TradingView pine script
                // These are in price points, not ticks
                EmaPeriod = 8;
                LongTrailPoints = 60;
                LongTrailOffset = 1;
                LongStopLoss = 100;
                ShortTrailPoints = 40;
                ShortTrailOffset = 1;
                ShortStopLoss = 70;
                
                // Initialize variables
                isInLongPosition = false;
                isInShortPosition = false;
                longTrailingActive = false;
                shortTrailingActive = false;
            }
            else if (State == State.DataLoaded)
            {
                ema = EMA(EmaPeriod);
                
                // Determine point size based on instrument
                // For MES, 1 point = 4 ticks (0.25 index points)
                // For ES, 1 point = 4 ticks (1.00 index points)
                // For MGC, 1 point = 10 ticks (0.1 troy ounce)
                if (Instrument.MasterInstrument.Name.Contains("MES"))
                    pointSize = 4; // 1 point = 4 ticks for MES
                else if (Instrument.MasterInstrument.Name.Contains("ES"))
                    pointSize = 4; // 1 point = 4 ticks for ES
                else if (Instrument.MasterInstrument.Name.Contains("MGC"))
                    pointSize = 10; // 1 point = 10 ticks for MGC
                else
                    pointSize = 1; // Default: assume 1 point = 1 tick
                
                Print($"Instrument: {Instrument.MasterInstrument.Name}, Point Size: {pointSize} ticks");
            }
        }

        protected override void OnBarUpdate()
        {
            // Make sure we have enough bars to work with
            if (CurrentBar < 20)
                return;

            // Calculate Camarilla levels using previous day's data
            // This is the exact formula from the pine script
            double prevHigh = High[1];
            double prevLow = Low[1];
            double prevClose = Close[1];
            
            // Calculate H4 and L4 exactly as in pine script
            h4 = prevClose + (prevHigh - prevLow) * 1.1 / 2.0;
            l4 = prevClose - (prevHigh - prevLow) * 1.1 / 2.0;
            
            // Debug output to verify calculations
            if (IsFirstTickOfBar)
            {
                Print($"Date: {Time[0]}, Prev High: {prevHigh}, Prev Low: {prevLow}, Prev Close: {prevClose}");
                Print($"H4: {h4}, L4: {l4}, Current Close: {Close[0]}, Current Open: {Open[0]}, EMA: {ema[0]}");
            }

            // Entry signals only on bar close (like in TradingView)
            if (Calculate == Calculate.OnBarClose || IsFirstTickOfBar)
            {
                // Long entry condition - matching TradingView pine script exactly
                if (Close[0] > h4 && Open[0] < h4 && ema[0] < Close[0] && Position.MarketPosition == MarketPosition.Flat)
                {
                    EnterLong(1, "Long");
                    entryPrice = Close[0];
                    highestSinceEntry = Close[0];
                    isInLongPosition = true;
                    isInShortPosition = false;
                    longTrailingActive = false;
                    
                    // Log entry
                    Print($"LONG ENTRY: {Instrument.FullName} at {Close[0]}, Time: {Time[0]}");
                    Print($"Entry Conditions: Close({Close[0]}) > H4({h4}), Open({Open[0]}) < H4({h4}), EMA({ema[0]}) < Close({Close[0]})");
                }
                // Short entry condition - matching TradingView pine script exactly
                else if (Close[0] < l4 && Open[0] > l4 && ema[0] > Close[0] && Position.MarketPosition == MarketPosition.Flat)
                {
                    EnterShort(1, "Short");
                    entryPrice = Close[0];
                    lowestSinceEntry = Close[0];
                    isInShortPosition = true;
                    isInLongPosition = false;
                    shortTrailingActive = false;
                    
                    // Log entry
                    Print($"SHORT ENTRY: {Instrument.FullName} at {Close[0]}, Time: {Time[0]}");
                    Print($"Entry Conditions: Close({Close[0]}) < L4({l4}), Open({Open[0]}) > L4({l4}), EMA({ema[0]}) > Close({Close[0]})");
                }
            }

            // Trailing stop logic runs on each tick (like in TradingView)
            // Manage trailing stop for long
            if (Position.MarketPosition == MarketPosition.Long && isInLongPosition)
            {
                if (Close[0] > highestSinceEntry)
                    highestSinceEntry = Close[0];

                // CORRECTED: Convert points to ticks using pointSize
                // Activate trailing stop only after price moves LongTrailPoints points in our favor
                double longTrailPointsInTicks = LongTrailPoints * pointSize;
                if (!longTrailingActive && Close[0] >= entryPrice + (longTrailPointsInTicks * TickSize))
                {
                    longTrailingActive = true;
                    Print($"LONG TRAIL ACTIVATED: Price={Close[0]}, Entry={entryPrice}, Points={LongTrailPoints} points ({longTrailPointsInTicks} ticks)");
                }

                if (longTrailingActive)
                {
                    // CORRECTED: Convert points to ticks using pointSize
                    // Set trailing stop LongTrailOffset points behind the highest price
                    double longTrailOffsetInTicks = LongTrailOffset * pointSize;
                    double newStop = highestSinceEntry - (longTrailOffsetInTicks * TickSize);
                    ExitLongStopMarket(0, true, Position.Quantity, newStop, "ExitLongTrail", "Long");
                }

                // Fixed stop loss - convert points to ticks
                double longStopLossInTicks = LongStopLoss * pointSize;
                if (Close[0] <= entryPrice - (longStopLossInTicks * TickSize))
                {
                    ExitLong("ExitLongStop", "Long");
                    Print($"LONG STOP LOSS: Price={Close[0]}, Entry={entryPrice}, Loss={LongStopLoss} points ({longStopLossInTicks} ticks)");
                    isInLongPosition = false;
                }
            }

            // Manage trailing stop for short
            if (Position.MarketPosition == MarketPosition.Short && isInShortPosition)
            {
                if (Close[0] < lowestSinceEntry)
                    lowestSinceEntry = Close[0];

                // CORRECTED: Convert points to ticks using pointSize
                // Activate trailing stop only after price moves ShortTrailPoints points in our favor
                double shortTrailPointsInTicks = ShortTrailPoints * pointSize;
                if (!shortTrailingActive && Close[0] <= entryPrice - (shortTrailPointsInTicks * TickSize))
                {
                    shortTrailingActive = true;
                    Print($"SHORT TRAIL ACTIVATED: Price={Close[0]}, Entry={entryPrice}, Points={ShortTrailPoints} points ({shortTrailPointsInTicks} ticks)");
                }

                if (shortTrailingActive)
                {
                    // CORRECTED: Convert points to ticks using pointSize
                    // Set trailing stop ShortTrailOffset points behind the lowest price
                    double shortTrailOffsetInTicks = ShortTrailOffset * pointSize;
                    double newStop = lowestSinceEntry + (shortTrailOffsetInTicks * TickSize);
                    ExitShortStopMarket(0, true, Position.Quantity, newStop, "ExitShortTrail", "Short");
                }

                // Fixed stop loss - convert points to ticks
                double shortStopLossInTicks = ShortStopLoss * pointSize;
                if (Close[0] >= entryPrice + (shortStopLossInTicks * TickSize))
                {
                    ExitShort("ExitShortStop", "Short");
                    Print($"SHORT STOP LOSS: Price={Close[0]}, Entry={entryPrice}, Loss={ShortStopLoss} points ({shortStopLossInTicks} ticks)");
                    isInShortPosition = false;
                }
            }
            
            // Reset position flags if we're flat
            if (Position.MarketPosition == MarketPosition.Flat)
            {
                isInLongPosition = false;
                isInShortPosition = false;
            }
        }
        
        #region Properties
        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "EMA Period", Description = "Period for the EMA indicator", Order = 1, GroupName = "Parameters")]
        public int EmaPeriod { get; set; }
        
        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "Long Trail Points", Description = "Trail points for long positions (in price points, not ticks)", Order = 2, GroupName = "Parameters")]
        public int LongTrailPoints { get; set; }
        
        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "Long Trail Offset", Description = "Trail offset for long positions (in price points, not ticks)", Order = 3, GroupName = "Parameters")]
        public int LongTrailOffset { get; set; }
        
        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "Long Stop Loss", Description = "Stop loss for long positions (in price points, not ticks)", Order = 4, GroupName = "Parameters")]
        public int LongStopLoss { get; set; }
        
        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "Short Trail Points", Description = "Trail points for short positions (in price points, not ticks)", Order = 5, GroupName = "Parameters")]
        public int ShortTrailPoints { get; set; }
        
        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "Short Trail Offset", Description = "Trail offset for short positions (in price points, not ticks)", Order = 6, GroupName = "Parameters")]
        public int ShortTrailOffset { get; set; }
        
        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "Short Stop Loss", Description = "Stop loss for short positions (in price points, not ticks)", Order = 7, GroupName = "Parameters")]
        public int ShortStopLoss { get; set; }
        #endregion
    }
}
