
#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.Indicators;
using NinjaTrader.NinjaScript.DrawingTools;
#endregion

//This namespace holds Strategies in this folder and is required. Do not change it.
namespace NinjaTrader.NinjaScript.Strategies
{
    public class CamarillaStrategy : Strategy
    {
        // Variables for Camarilla levels
        private double h4;
        private double l4;

        // Variables for entry and exit
        private double entryPrice;
        private double highestSinceEntry;
        private double lowestSinceEntry;
        private bool isInLongPosition = false;
        private bool isInShortPosition = false;
        private bool longTrailingActive = false;
        private bool shortTrailingActive = false;

        // Point size for converting points to ticks
        private double pointSize = 1;

        // EMA indicator
        private EMA ema;

        protected override void OnStateChange()
        {
            if (State == State.SetDefaults)
            {
                Name = "CamarillaStrategy";
                Description = "Camarilla Strategy with fixed H4/L4 levels";
                Calculate = Calculate.OnEachTick; // Must be OnEachTick for trailing stop to work properly
                EntriesPerDirection = 1;
                EntryHandling = EntryHandling.AllEntries;
                IsExitOnSessionCloseStrategy = true;
                ExitOnSessionCloseSeconds = 30;
                IsFillLimitOnTouch = false;
                MaximumBarsLookBack = MaximumBarsLookBack.TwoHundredFiftySix;
                OrderFillResolution = OrderFillResolution.Standard;
                Slippage = 0;
                StartBehavior = StartBehavior.WaitUntilFlat;
                TimeInForce = TimeInForce.Gtc;
                TraceOrders = true; // Enable order tracing for better debugging
                RealtimeErrorHandling = RealtimeErrorHandling.StopCancelClose;
                StopTargetHandling = StopTargetHandling.PerEntryExecution;
                BarsRequiredToTrade = 20;

                // Default parameter values
                EmaPeriod = 8;
                LongTrailPoints = 60;
                LongTrailOffset = 1;
                LongStopLoss = 100;
                ShortTrailPoints = 40;
                ShortTrailOffset = 1;
                ShortStopLoss = 70;

                // Initialize variables
                isInLongPosition = false;
                isInShortPosition = false;
                longTrailingActive = false;
                shortTrailingActive = false;
            }
            else if (State == State.Configure)
            {
                // Add EMA indicator
                ema = EMA(EmaPeriod);
                AddChartIndicator(ema);
            }
            else if (State == State.DataLoaded)
            {
                // Set point size based on instrument
                if (Instrument != null && Instrument.MasterInstrument != null)
                {
                    pointSize = Instrument.MasterInstrument.PointValue;
                    Print($"Point size for {Instrument.FullName}: {pointSize}");
                }
            }
        }

        protected override void OnBarUpdate()
        {
            try
            {
                // Skip processing if we don't have enough bars
                if (BarsInProgress != 0 || CurrentBar < 20)
                    return;

                // No debug text on chart - it's too cluttered

                // Get current bar date
                DateTime currentDate = Time[0].Date;

                // Set H4/L4 values based on date - using EXACT values you provided
                if (currentDate.Date == new DateTime(2025, 5, 13))
                {
                    // Tuesday 13.5.2025
                    h4 = 5943.2375;
                    l4 = 5786.7625;
                }
                else if (currentDate.Date == new DateTime(2025, 5, 14))
                {
                    // Wednesday 14.5.2025
                    h4 = 5954.55;
                    l4 = 5854.45;
                }
                else if (currentDate.Date == new DateTime(2025, 5, 15))
                {
                    // Thursday 15.5.2025
                    h4 = 5927.75;
                    l4 = 5889.25;
                }
                else if (currentDate.Date == new DateTime(2025, 5, 16))
                {
                    // Friday 16.5.2025
                    h4 = 5975.875;
                    l4 = 5890.625;
                }
                else
                {
                    // Default values for other dates
                    h4 = 5900;
                    l4 = 5800;
                    Print($"Using default H4/L4 values for {currentDate.ToShortDateString()} - no specific data available");
                }

                // Find the first and last bar of the current day
                int firstBarOfDay = -1;
                int lastBarOfDay = -1;

                // Find the first bar of the day
                for (int i = 0; i < Math.Min(CurrentBar + 1, 1000); i++)
                {
                    if (Time[i].Date == currentDate && (i == CurrentBar || Time[i+1].Date != currentDate))
                    {
                        firstBarOfDay = i;
                        break;
                    }
                }

                // Find the last bar of the day
                for (int i = 0; i < Math.Min(Bars.Count - 1, 1000); i++)
                {
                    if (Time[i].Date == currentDate && (i == Bars.Count - 1 || Time[i+1].Date != currentDate))
                    {
                        lastBarOfDay = i;
                        break;
                    }
                }

                // Draw H4/L4 levels on first bar of the day or when the date changes
                if (CurrentBar == 0 || Time[1].Date != currentDate)
                {
                    string dateStr = currentDate.ToString("yyyyMMdd");

                    // Remove old lines if they exist
                    RemoveDrawObject("H4_" + dateStr);
                    RemoveDrawObject("L4_" + dateStr);
                    RemoveDrawObject("H4_Text_" + dateStr);
                    RemoveDrawObject("L4_Text_" + dateStr);

                    // If we know both first and last bar of the day
                    if (firstBarOfDay >= 0 && lastBarOfDay >= 0)
                    {
                        // Draw lines from first to last bar of the day
                        Draw.Line(this, "H4_" + dateStr, false, firstBarOfDay, h4, lastBarOfDay, h4, Brushes.Orange, DashStyleHelper.Solid, 2);
                        Draw.Line(this, "L4_" + dateStr, false, firstBarOfDay, l4, lastBarOfDay, l4, Brushes.Orange, DashStyleHelper.Solid, 2);

                        Print($"Drew H4/L4 lines for {currentDate.ToShortDateString()}: H4={h4}, L4={l4}, FirstBar={firstBarOfDay}, LastBar={lastBarOfDay}");
                    }
                    else if (firstBarOfDay >= 0)
                    {
                        // We only know the first bar, draw a line from first bar to current bar
                        Draw.Line(this, "H4_" + dateStr, false, firstBarOfDay, h4, 0, h4, Brushes.Orange, DashStyleHelper.Solid, 2);
                        Draw.Line(this, "L4_" + dateStr, false, firstBarOfDay, l4, 0, l4, Brushes.Orange, DashStyleHelper.Solid, 2);

                        Print($"Drew H4/L4 lines for {currentDate.ToShortDateString()}: H4={h4}, L4={l4}, FirstBar={firstBarOfDay}, LastBar=current");
                    }

                    // Add small text labels with values
                    Draw.Text(this, "H4_Text_" + dateStr, h4.ToString("0.00"),
                        firstBarOfDay >= 0 ? firstBarOfDay : 0, h4 + 5 * TickSize, Brushes.Orange);
                    Draw.Text(this, "L4_Text_" + dateStr, l4.ToString("0.00"),
                        firstBarOfDay >= 0 ? firstBarOfDay : 0, l4 - 5 * TickSize, Brushes.Orange);
                }

                // Check entry conditions
                bool longCondition1 = Close[0] > h4;
                bool longCondition2 = Open[0] < h4;
                bool longCondition3 = ema[0] < Close[0];
                bool shortCondition1 = Close[0] < l4;
                bool shortCondition2 = Open[0] > l4;
                bool shortCondition3 = ema[0] > Close[0];

                // No debug text on chart - it's too cluttered

                // Debug output only to log, not on chart - but more detailed for troubleshooting
                if (IsFirstTickOfBar) // Log every bar for better debugging
                {
                    Print($"Bar: {CurrentBar}, Date: {Time[0].Date.ToShortDateString()}, Time: {Time[0].TimeOfDay}");
                    Print($"H4: {h4}, L4: {l4}, Current Close: {Close[0]}, Current Open: {Open[0]}, EMA: {ema[0]}");
                    Print($"Long conditions: C>H4:{longCondition1}, O<H4:{longCondition2}, EMA<C:{longCondition3}, All:{longCondition1 && longCondition2 && longCondition3}");
                    Print($"Short conditions: C<L4:{shortCondition1}, O>L4:{shortCondition2}, EMA>C:{shortCondition3}, All:{shortCondition1 && shortCondition2 && shortCondition3}");
                    Print($"Position: {Position.MarketPosition}, Quantity: {Position.Quantity}, Flat:{Position.MarketPosition == MarketPosition.Flat}");
                    Print($"Position flags: isInLongPosition={isInLongPosition}, isInShortPosition={isInShortPosition}");

                    // Add more detailed debugging for entry conditions
                    if (longCondition1 && longCondition2 && longCondition3)
                    {
                        Print($"*** LONG ENTRY CONDITIONS MET: Flat={Position.MarketPosition == MarketPosition.Flat}, Will enter={longCondition1 && longCondition2 && longCondition3 && Position.MarketPosition == MarketPosition.Flat}");

                        // Check if we're really flat but our flags say otherwise
                        if (Position.MarketPosition == MarketPosition.Flat && (isInLongPosition || isInShortPosition))
                        {
                            Print($"*** WARNING: Position is FLAT but flags say we're in a position! Resetting flags...");
                            isInLongPosition = false;
                            isInShortPosition = false;
                        }
                    }

                    if (shortCondition1 && shortCondition2 && shortCondition3)
                    {
                        Print($"*** SHORT ENTRY CONDITIONS MET: Flat={Position.MarketPosition == MarketPosition.Flat}, Will enter={shortCondition1 && shortCondition2 && shortCondition3 && Position.MarketPosition == MarketPosition.Flat}");

                        // Check if we're really flat but our flags say otherwise
                        if (Position.MarketPosition == MarketPosition.Flat && (isInLongPosition || isInShortPosition))
                        {
                            Print($"*** WARNING: Position is FLAT but flags say we're in a position! Resetting flags...");
                            isInLongPosition = false;
                            isInShortPosition = false;
                        }
                    }
                }

                // SUPER SIMPLIFIED ENTRY CONDITIONS FOR TESTING
                // Long entry condition - ONLY check conditions, ignore position flags
                if (longCondition1 && longCondition2 && longCondition3 && Position.MarketPosition == MarketPosition.Flat)
                {
                    // Force close any existing position first
                    if (Position.MarketPosition != MarketPosition.Flat)
                    {
                        if (Position.MarketPosition == MarketPosition.Long)
                            ExitLong();
                        else if (Position.MarketPosition == MarketPosition.Short)
                            ExitShort();
                    }

                    // Reset all flags
                    isInLongPosition = false;
                    isInShortPosition = false;
                    longTrailingActive = false;
                    shortTrailingActive = false;

                    // Enter long position
                    EnterLong(1, "Long");
                    entryPrice = Close[0];
                    highestSinceEntry = Close[0];
                    isInLongPosition = true;

                    // Draw entry arrow - make it VERY visible
                    Draw.ArrowUp(this, "LongEntry_" + CurrentBar, true, 0, Low[0] - 10 * TickSize, Brushes.Green);
                    Draw.Text(this, "LongEntryText_" + CurrentBar, "LONG ENTRY", 0, Low[0] - 20 * TickSize, Brushes.Green);

                    // Log entry
                    Print($"LONG ENTRY: {Instrument.FullName} at {Close[0]}, Time: {Time[0]}");
                }
                // Short entry condition - ONLY check conditions, ignore position flags
                else if (shortCondition1 && shortCondition2 && shortCondition3 && Position.MarketPosition == MarketPosition.Flat)
                {
                    // Force close any existing position first
                    if (Position.MarketPosition != MarketPosition.Flat)
                    {
                        if (Position.MarketPosition == MarketPosition.Long)
                            ExitLong();
                        else if (Position.MarketPosition == MarketPosition.Short)
                            ExitShort();
                    }

                    // Reset all flags
                    isInLongPosition = false;
                    isInShortPosition = false;
                    longTrailingActive = false;
                    shortTrailingActive = false;

                    // Enter short position
                    EnterShort(1, "Short");
                    entryPrice = Close[0];
                    lowestSinceEntry = Close[0];
                    isInShortPosition = true;

                    // Draw entry arrow - make it VERY visible
                    Draw.ArrowDown(this, "ShortEntry_" + CurrentBar, true, 0, High[0] + 10 * TickSize, Brushes.Red);
                    Draw.Text(this, "ShortEntryText_" + CurrentBar, "SHORT ENTRY", 0, High[0] + 20 * TickSize, Brushes.Red);

                    // Log entry
                    Print($"SHORT ENTRY: {Instrument.FullName} at {Close[0]}, Time: {Time[0]}");
                }

                // Trailing stop logic
                // Manage trailing stop for long
                if (Position.MarketPosition == MarketPosition.Long && isInLongPosition)
                {
                    if (Close[0] > highestSinceEntry)
                        highestSinceEntry = Close[0];

                    // Convert points to ticks using pointSize
                    // Activate trailing stop only after price moves LongTrailPoints points in our favor
                    double longTrailPointsInTicks = LongTrailPoints * pointSize;
                    if (!longTrailingActive && Close[0] >= entryPrice + (longTrailPointsInTicks * TickSize))
                    {
                        longTrailingActive = true;
                        Print($"LONG TRAIL ACTIVATED: Price={Close[0]}, Entry={entryPrice}, Points={LongTrailPoints} points ({longTrailPointsInTicks} ticks)");
                    }

                    if (longTrailingActive)
                    {
                        // Convert points to ticks using pointSize
                        // Set trailing stop LongTrailOffset points behind the highest price
                        double longTrailOffsetInTicks = LongTrailOffset * pointSize;
                        double newStop = highestSinceEntry - (longTrailOffsetInTicks * TickSize);
                        ExitLongStopMarket(0, true, Position.Quantity, newStop, "ExitLongTrail", "Long");

                        // Draw trailing stop line - only update once per bar to reduce clutter
                        if (IsFirstTickOfBar)
                            Draw.Line(this, "LongTrailStop", false, 0, newStop, 10, newStop, Brushes.Blue, DashStyleHelper.Dash, 1);
                    }

                    // Fixed stop loss - convert points to ticks
                    double longStopLossInTicks = LongStopLoss * pointSize;
                    double stopPrice = entryPrice - (longStopLossInTicks * TickSize);

                    // Draw stop loss line - only once when position is opened
                    if (IsFirstTickOfBar && Position.MarketPosition == MarketPosition.Long && Math.Abs(Position.AveragePrice - entryPrice) < 0.0001)
                        Draw.Line(this, "LongStopLoss", false, 0, stopPrice, 10, stopPrice, Brushes.Red, DashStyleHelper.Dash, 1);

                    // Check if stop loss hit
                    if (Close[0] <= stopPrice)
                    {
                        ExitLong("ExitLongStop", "Long");
                        Print($"LONG STOP LOSS: Price={Close[0]}, Entry={entryPrice}, Loss={LongStopLoss} points ({longStopLossInTicks} ticks)");
                        isInLongPosition = false;
                    }
                }

                // Manage trailing stop for short
                if (Position.MarketPosition == MarketPosition.Short && isInShortPosition)
                {
                    if (Close[0] < lowestSinceEntry)
                        lowestSinceEntry = Close[0];

                    // Convert points to ticks using pointSize
                    // Activate trailing stop only after price moves ShortTrailPoints points in our favor
                    double shortTrailPointsInTicks = ShortTrailPoints * pointSize;
                    if (!shortTrailingActive && Close[0] <= entryPrice - (shortTrailPointsInTicks * TickSize))
                    {
                        shortTrailingActive = true;
                        Print($"SHORT TRAIL ACTIVATED: Price={Close[0]}, Entry={entryPrice}, Points={ShortTrailPoints} points ({shortTrailPointsInTicks} ticks)");
                    }

                    if (shortTrailingActive)
                    {
                        // Convert points to ticks using pointSize
                        // Set trailing stop ShortTrailOffset points behind the lowest price
                        double shortTrailOffsetInTicks = ShortTrailOffset * pointSize;
                        double newStop = lowestSinceEntry + (shortTrailOffsetInTicks * TickSize);
                        ExitShortStopMarket(0, true, Position.Quantity, newStop, "ExitShortTrail", "Short");

                        // Draw trailing stop line - only update once per bar to reduce clutter
                        if (IsFirstTickOfBar)
                            Draw.Line(this, "ShortTrailStop", false, 0, newStop, 10, newStop, Brushes.Blue, DashStyleHelper.Dash, 1);
                    }

                    // Fixed stop loss - convert points to ticks
                    double shortStopLossInTicks = ShortStopLoss * pointSize;
                    double stopPrice = entryPrice + (shortStopLossInTicks * TickSize);

                    // Draw stop loss line - only once when position is opened
                    if (IsFirstTickOfBar && Position.MarketPosition == MarketPosition.Short && Math.Abs(Position.AveragePrice - entryPrice) < 0.0001)
                        Draw.Line(this, "ShortStopLoss", false, 0, stopPrice, 10, stopPrice, Brushes.Red, DashStyleHelper.Dash, 1);

                    // Check if stop loss hit
                    if (Close[0] >= stopPrice)
                    {
                        ExitShort("ExitShortStop", "Short");
                        Print($"SHORT STOP LOSS: Price={Close[0]}, Entry={entryPrice}, Loss={ShortStopLoss} points ({shortStopLossInTicks} ticks)");
                        isInShortPosition = false;
                    }
                }

                // Reset position flags if we're flat - CRITICAL for allowing new entries
                if (Position.MarketPosition == MarketPosition.Flat)
                {
                    // If we were in a position before and now we're flat, log it
                    if (isInLongPosition || isInShortPosition)
                    {
                        Print($"POSITION CLOSED: Was in {(isInLongPosition ? "LONG" : "SHORT")}, now FLAT. Ready for new entries.");
                        Print($"Position state after close: MarketPosition={Position.MarketPosition}, Quantity={Position.Quantity}");

                        // Force reset all position flags to ensure we can enter new positions
                        isInLongPosition = false;
                        isInShortPosition = false;
                        longTrailingActive = false;
                        shortTrailingActive = false;

                        // Reset entry price to avoid confusion
                        entryPrice = 0;
                        highestSinceEntry = 0;
                        lowestSinceEntry = 0;

                        Print($"All position flags reset. Ready for new entries.");
                    }
                    else
                    {
                        // Double-check that all flags are reset even if we think we're not in a position
                        // This is a safety measure to ensure we can always enter new positions when flat
                        isInLongPosition = false;
                        isInShortPosition = false;
                        longTrailingActive = false;
                        shortTrailingActive = false;
                    }
                }
            }
            catch (Exception ex)
            {
                Print($"Error in OnBarUpdate: {ex.Message}");
            }
        }

        #region Properties
        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name="EMA Period", Description="Period for EMA indicator", Order=1, GroupName="Parameters")]
        public int EmaPeriod
        { get; set; }

        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name="Long Trail Points", Description="Trail points for long positions (in price points, not ticks)", Order=2, GroupName="Parameters")]
        public int LongTrailPoints
        { get; set; }

        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name="Long Trail Offset", Description="Trail offset for long positions (in price points, not ticks)", Order=3, GroupName="Parameters")]
        public int LongTrailOffset
        { get; set; }

        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name="Long Stop Loss", Description="Stop loss for long positions (in price points, not ticks)", Order=4, GroupName="Parameters")]
        public int LongStopLoss
        { get; set; }

        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name="Short Trail Points", Description="Trail points for short positions (in price points, not ticks)", Order=5, GroupName="Parameters")]
        public int ShortTrailPoints
        { get; set; }

        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name="Short Trail Offset", Description="Trail offset for short positions (in price points, not ticks)", Order=6, GroupName="Parameters")]
        public int ShortTrailOffset
        { get; set; }

        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name="Short Stop Loss", Description="Stop loss for short positions (in price points, not ticks)", Order=7, GroupName="Parameters")]
        public int ShortStopLoss
        { get; set; }
        #endregion
    }
}
