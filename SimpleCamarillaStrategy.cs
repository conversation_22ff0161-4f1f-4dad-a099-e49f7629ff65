#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.Indicators;
using NinjaTrader.NinjaScript.DrawingTools;
#endregion

//This namespace holds Strategies in this folder and is required. Do not change it. 
namespace NinjaTrader.NinjaScript.Strategies
{
    public class SimpleCamarillaStrategy : Strategy
    {
        // Variables for Camarilla levels
        private double h4;
        private double l4;
        
        // Variables for entry and exit
        private double entryPrice;
        private bool isInLongPosition = false;
        private bool isInShortPosition = false;
        
        // Point size for converting points to ticks
        private double pointSize = 1;
        
        // EMA indicator
        private EMA ema;
        
        protected override void OnStateChange()
        {
            if (State == State.SetDefaults)
            {
                Description = "Simple Camarilla Strategy with fixed H4/L4 levels";
                Name = "SimpleCamarillaStrategy";
                Calculate = Calculate.OnBarClose;
                EntriesPerDirection = 1;
                EntryHandling = EntryHandling.AllEntries;
                IsExitOnSessionCloseStrategy = true;
                ExitOnSessionCloseSeconds = 30;
                IsFillLimitOnTouch = false;
                MaximumBarsLookBack = MaximumBarsLookBack.TwoHundredFiftySix;
                OrderFillResolution = OrderFillResolution.Standard;
                Slippage = 0;
                StartBehavior = StartBehavior.WaitUntilFlat;
                TimeInForce = TimeInForce.Gtc;
                TraceOrders = false;
                RealtimeErrorHandling = RealtimeErrorHandling.StopCancelClose;
                StopTargetHandling = StopTargetHandling.PerEntryExecution;
                BarsRequiredToTrade = 20;
                
                // Default parameter values
                EMAPeriod = 9;
                LongStopLoss = 100;
                ShortStopLoss = 70;
            }
            else if (State == State.Configure)
            {
                // Add EMA indicator
                ema = EMA(EMAPeriod);
                AddChartIndicator(ema);
            }
            else if (State == State.DataLoaded)
            {
                // Set point size based on instrument
                if (Instrument != null && Instrument.MasterInstrument != null)
                {
                    pointSize = Instrument.MasterInstrument.PointValue;
                    Print($"Point size for {Instrument.FullName}: {pointSize}");
                }
            }
        }
        
        protected override void OnBarUpdate()
        {
            try
            {
                // Skip processing if we don't have enough bars
                if (BarsInProgress != 0 || CurrentBar < 20)
                    return;
                
                // Get current bar date
                DateTime currentDate = Time[0].Date;
                
                // Set H4/L4 values based on date - using exact values you provided
                if (currentDate.Date == new DateTime(2025, 5, 9))
                {
                    h4 = 5731.5;
                    l4 = 5624.5;
                }
                else if (currentDate.Date == new DateTime(2025, 5, 12))
                {
                    h4 = 5935.1;
                    l4 = 5794.9;
                }
                else if (currentDate.Date == new DateTime(2025, 5, 13))
                {
                    h4 = 5954.8;
                    l4 = 5854.2;
                }
                else if (currentDate.Date == new DateTime(2025, 5, 14))
                {
                    h4 = 5927.3;
                    l4 = 5889.7;
                }
                else if (currentDate.Date == new DateTime(2025, 5, 15))
                {
                    h4 = 5975.7;
                    l4 = 5890.8;
                }
                else if (currentDate.Date == new DateTime(2025, 5, 16))
                {
                    h4 = 5976.4;
                    l4 = 5937.6;
                }
                else
                {
                    // Default values for other dates
                    h4 = 5900;
                    l4 = 5800;
                }
                
                // Draw H4/L4 levels on first bar of the day
                if (CurrentBar == 0 || Time[1].Date != currentDate)
                {
                    string dateStr = currentDate.ToString("yyyyMMdd");
                    
                    // Draw horizontal lines for H4/L4
                    Draw.HorizontalLine(this, "H4_" + dateStr, h4, Brushes.Orange, DashStyleHelper.Solid, 2);
                    Draw.HorizontalLine(this, "L4_" + dateStr, l4, Brushes.Orange, DashStyleHelper.Solid, 2);
                    
                    // Add text labels
                    Draw.Text(this, "H4_Text_" + dateStr, "H4 " + currentDate.ToString("MM/dd") + ": " + h4.ToString("0.00"), 
                        0, h4 + 10 * TickSize, Brushes.Orange);
                    Draw.Text(this, "L4_Text_" + dateStr, "L4 " + currentDate.ToString("MM/dd") + ": " + l4.ToString("0.00"), 
                        0, l4 - 10 * TickSize, Brushes.Orange);
                    
                    Print($"Drew H4/L4 lines for {currentDate.ToShortDateString()}: H4={h4}, L4={l4}");
                }
                
                // Check entry conditions
                bool longCondition1 = Close[0] > h4;
                bool longCondition2 = Open[0] < h4;
                bool longCondition3 = ema[0] < Close[0];
                bool shortCondition1 = Close[0] < l4;
                bool shortCondition2 = Open[0] > l4;
                bool shortCondition3 = ema[0] > Close[0];
                
                // Debug output for every bar
                if (IsFirstTickOfBar)
                {
                    Print($"Bar: {CurrentBar}, Date: {Time[0].Date.ToShortDateString()}, Time: {Time[0].TimeOfDay}");
                    Print($"H4: {h4}, L4: {l4}, Current Close: {Close[0]}, Current Open: {Open[0]}, EMA: {ema[0]}");
                    Print($"Long conditions: C>H4:{longCondition1}, O<H4:{longCondition2}, EMA<C:{longCondition3}, All:{longCondition1 && longCondition2 && longCondition3}");
                    Print($"Short conditions: C<L4:{shortCondition1}, O>L4:{shortCondition2}, EMA>C:{shortCondition3}, All:{shortCondition1 && shortCondition2 && shortCondition3}");
                    Print($"Position: {Position.MarketPosition}, Quantity: {Position.Quantity}");
                    
                    // Draw the conditions on the chart for better visibility
                    string longText = $"Long: {longCondition1 && longCondition2 && longCondition3}";
                    string shortText = $"Short: {shortCondition1 && shortCondition2 && shortCondition3}";
                    
                    Draw.Text(this, "LongCond_" + CurrentBar, longText, 0, High[0] + 20 * TickSize,
                        longCondition1 && longCondition2 && longCondition3 ? Brushes.Green : Brushes.Red);
                    Draw.Text(this, "ShortCond_" + CurrentBar, shortText, 0, Low[0] - 20 * TickSize,
                        shortCondition1 && shortCondition2 && shortCondition3 ? Brushes.Green : Brushes.Red);
                }
                
                // Long entry condition - matching TradingView pine script exactly
                if (longCondition1 && longCondition2 && longCondition3 && Position.MarketPosition == MarketPosition.Flat)
                {
                    EnterLong(1, "Long");
                    entryPrice = Close[0];
                    isInLongPosition = true;
                    isInShortPosition = false;
                    
                    // Draw entry arrow
                    Draw.ArrowUp(this, "LongEntry_" + CurrentBar, true, 0, Low[0] - 10 * TickSize, Brushes.Green);
                    Draw.Text(this, "LongEntryText_" + CurrentBar, "LONG ENTRY", 0, Low[0] - 20 * TickSize, Brushes.Green);
                    
                    // Log entry
                    Print($"LONG ENTRY: {Instrument.FullName} at {Close[0]}, Time: {Time[0]}");
                    Print($"Entry Conditions: Close({Close[0]}) > H4({h4}), Open({Open[0]}) < H4({h4}), EMA({ema[0]}) < Close({Close[0]})");
                }
                // Short entry condition - matching TradingView pine script exactly
                else if (shortCondition1 && shortCondition2 && shortCondition3 && Position.MarketPosition == MarketPosition.Flat)
                {
                    EnterShort(1, "Short");
                    entryPrice = Close[0];
                    isInShortPosition = true;
                    isInLongPosition = false;
                    
                    // Draw entry arrow
                    Draw.ArrowDown(this, "ShortEntry_" + CurrentBar, true, 0, High[0] + 10 * TickSize, Brushes.Red);
                    Draw.Text(this, "ShortEntryText_" + CurrentBar, "SHORT ENTRY", 0, High[0] + 20 * TickSize, Brushes.Red);
                    
                    // Log entry
                    Print($"SHORT ENTRY: {Instrument.FullName} at {Close[0]}, Time: {Time[0]}");
                    Print($"Entry Conditions: Close({Close[0]}) < L4({l4}), Open({Open[0]}) > L4({l4}), EMA({ema[0]}) > Close({Close[0]})");
                }
                
                // Simple fixed stop loss for long positions
                if (Position.MarketPosition == MarketPosition.Long && isInLongPosition)
                {
                    // Fixed stop loss - convert points to ticks
                    double longStopLossInTicks = LongStopLoss * pointSize;
                    double stopPrice = entryPrice - (longStopLossInTicks * TickSize);
                    
                    // Set stop loss
                    ExitLongStopMarket(0, true, Position.Quantity, stopPrice, "LongStopLoss", "Long");
                    
                    // Draw stop loss line
                    Draw.Line(this, "LongStopLoss_" + CurrentBar, false, 0, stopPrice, 10, stopPrice, Brushes.Red, DashStyleHelper.Solid, 1);
                }
                
                // Simple fixed stop loss for short positions
                if (Position.MarketPosition == MarketPosition.Short && isInShortPosition)
                {
                    // Fixed stop loss - convert points to ticks
                    double shortStopLossInTicks = ShortStopLoss * pointSize;
                    double stopPrice = entryPrice + (shortStopLossInTicks * TickSize);
                    
                    // Set stop loss
                    ExitShortStopMarket(0, true, Position.Quantity, stopPrice, "ShortStopLoss", "Short");
                    
                    // Draw stop loss line
                    Draw.Line(this, "ShortStopLoss_" + CurrentBar, false, 0, stopPrice, 10, stopPrice, Brushes.Red, DashStyleHelper.Solid, 1);
                }
                
                // Reset position flags if we're flat
                if (Position.MarketPosition == MarketPosition.Flat)
                {
                    isInLongPosition = false;
                    isInShortPosition = false;
                }
            }
            catch (Exception ex)
            {
                Print($"Error in OnBarUpdate: {ex.Message}");
            }
        }
        
        #region Properties
        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name="EMA Period", Description="Period for EMA indicator", Order=1, GroupName="Parameters")]
        public int EMAPeriod
        { get; set; }
        
        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name="Long Stop Loss", Description="Stop loss for long positions in points", Order=2, GroupName="Parameters")]
        public int LongStopLoss
        { get; set; }
        
        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name="Short Stop Loss", Description="Stop loss for short positions in points", Order=3, GroupName="Parameters")]
        public int ShortStopLoss
        { get; set; }
        #endregion
    }
}
