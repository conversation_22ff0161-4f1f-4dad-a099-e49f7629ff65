#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.Indicators;
using NinjaTrader.NinjaScript.DrawingTools;
using System.Net.Http;
using System.IO;
#endregion

//This namespace holds Strategies in this folder and is required. Do not change it.
namespace NinjaTrader.NinjaScript.Strategies
{
    public class CamarillaLiveStrategy : Strategy
    {
        // Variables for Camarilla levels
        private double h4;
        private double l4;

        // EMA indicator
        private EMA ema;



        // Variables for trailing stop
        private double entryPrice;
        private double highestSinceEntry;
        private double lowestSinceEntry;
        private bool longTrailingActive;
        private bool shortTrailingActive;
        private double currentTrailStop = 0;  // Aktuálna trailing stop úroveň

        // Cache for daily H4/L4 values to avoid recalculating on every tick
        private Dictionary<DateTime, Tuple<double, double>> dailyLevelsCache = new Dictionary<DateTime, Tuple<double, double>>();

        // Track drawn levels to prevent duplicates
        private HashSet<string> drawnLevels = new HashSet<string>();

        // Global control to prevent duplicate drawing across all instruments
        private static HashSet<string> globalDrawnDates = new HashSet<string>();

        // OPRAVA: Globálna kontrola kreslenia levelov pre všetky nástroje
        private static HashSet<string> globalDrawnLevels = new HashSet<string>();
        private static readonly object lockObject = new object();

        // Cache for session times to avoid recalculating
        private Dictionary<DateTime, Tuple<DateTime, DateTime>> sessionTimesCache = new Dictionary<DateTime, Tuple<DateTime, DateTime>>();

        // OPRAVA: Premenné pre rozlíšenie nových vs existujúcich pozícií
        private bool isNewPosition = false;
        private bool strategyStarted = false;
        private bool existingPositionProcessed = false;

        // Telegram notification variables
        private static readonly HttpClient httpClient = new HttpClient();

        // Simple flag to track if we're in live trading mode
        private bool isLiveTrading = false;

        // Flags to prevent multiple status notifications
        private bool startNotificationSent = false;
        private bool stopNotificationSent = false;

        // Async method to send Telegram notification
        private async Task SendTelegramNotificationAsync(string message)
        {
            try
            {
                if (string.IsNullOrEmpty(TelegramBotToken) || string.IsNullOrEmpty(TelegramChatId))
                {
                    LogToFile("Telegram notification skipped - Bot Token or Chat ID not configured");
                    return;
                }

                string url = $"https://api.telegram.org/bot{TelegramBotToken}/sendMessage";
                string payload = $"{{\"chat_id\":\"{TelegramChatId}\",\"text\":\"{message}\",\"parse_mode\":\"HTML\"}}";

                var content = new StringContent(payload, Encoding.UTF8, "application/json");
                var response = await httpClient.PostAsync(url, content);

                if (response.IsSuccessStatusCode)
                {
                    LogToFile($"Telegram notification sent successfully: {message}");
                }
                else
                {
                    LogToFile($"Failed to send Telegram notification. Status: {response.StatusCode}");
                }
            }
            catch (Exception ex)
            {
                LogToFile($"Error sending Telegram notification: {ex.Message}");
            }
        }

        // Simple wrapper for Telegram notifications - only send if live trading
        private void SendTelegramNotification(string message)
        {
            // Only send notifications during live trading (not historical data processing)
            if (!isLiveTrading)
            {
                LogToFile($"Telegram notification skipped - not live trading: {message.Split('\n')[0]}");
                return;
            }

            Task.Run(() => SendTelegramNotificationAsync(message));
        }

        // Method to determine decimal places based on instrument
        private int GetDecimalPlaces()
        {
            string instrumentName = Instrument.FullName.ToUpper();

            // Forex instruments need more decimal places
            if (instrumentName.Contains("EUR") || instrumentName.Contains("GBP") ||
                instrumentName.Contains("USD") || instrumentName.Contains("JPY") ||
                instrumentName.Contains("CHF") || instrumentName.Contains("CAD") ||
                instrumentName.Contains("AUD") || instrumentName.Contains("NZD") ||
                instrumentName.Contains("M6E") || instrumentName.Contains("M6B") ||
                instrumentName.Contains("M6A") || instrumentName.Contains("M6J"))
            {
                return 5; // 5 decimal places for forex
            }

            // Futures and other instruments
            return 2; // 2 decimal places for futures
        }

        // Method to get session times using SessionIterator
        private Tuple<DateTime, DateTime> GetSessionTimes(DateTime targetDate)
        {
            // Check cache first
            if (sessionTimesCache.ContainsKey(targetDate))
            {
                return sessionTimesCache[targetDate];
            }

            DateTime sessionStart = DateTime.MinValue;
            DateTime sessionEnd = DateTime.MaxValue;

            try
            {
                // Create SessionIterator for the target date
                SessionIterator sessionIterator = new SessionIterator(Bars.TradingHours);

                // Get session for the target date
                sessionIterator.GetNextSession(targetDate, false);

                if (sessionIterator.IsInSession(targetDate, false, true))
                {
                    // If targetDate is in session, use this session
                    sessionStart = sessionIterator.ActualSessionBegin;
                    sessionEnd = sessionIterator.ActualSessionEnd;
                }
                else
                {
                    // If not, find the previous session
                    sessionIterator.GetNextSession(targetDate.AddDays(-1), false);
                    sessionStart = sessionIterator.ActualSessionBegin;
                    sessionEnd = sessionIterator.ActualSessionEnd;
                }

                LogToFile($"SessionIterator for {targetDate:yyyy-MM-dd}: {sessionStart:yyyy-MM-dd HH:mm:ss} to {sessionEnd:yyyy-MM-dd HH:mm:ss}");
            }
            catch (Exception ex)
            {
                // Fallback to manual times if SessionIterator fails
                LogToFile($"SessionIterator failed for {targetDate:yyyy-MM-dd}: {ex.Message}, using fallback times");
                sessionStart = targetDate.AddDays(-1).Date.AddHours(18); // 18:00 previous day
                sessionEnd = targetDate.Date.AddHours(17); // 17:00 current day
                LogToFile($"Fallback session times: {sessionStart:yyyy-MM-dd HH:mm:ss} to {sessionEnd:yyyy-MM-dd HH:mm:ss}");
            }

            // Cache the result
            var sessionTimes = new Tuple<DateTime, DateTime>(sessionStart, sessionEnd);
            sessionTimesCache[targetDate] = sessionTimes;

            return sessionTimes;
        }

        // Custom log method to write to file
        private void LogToFile(string message)
        {
            try
            {
                string logPath = @"C:\Users\<USER>\Documents\augment-projects\Ninja bot\camarilla_log.txt";
                using (System.IO.StreamWriter sw = System.IO.File.AppendText(logPath))
                {
                    sw.WriteLine($"{DateTime.Now}: [{Instrument.FullName}] {message}");
                }
            }
            catch (Exception ex)
            {
                Print($"Error writing to log file: {ex.Message}");
            }
        }

        // OPRAVA: Metóda pre resetovanie trailing premenných
        private void ResetTrailingVariables()
        {
            entryPrice = 0;
            highestSinceEntry = 0;
            lowestSinceEntry = 0;
            longTrailingActive = false;
            shortTrailingActive = false;
            currentTrailStop = 0;
            isNewPosition = false;
            existingPositionProcessed = false;

            // Vyčistíme cache aby sa zabránilo duplicitným levelom
            dailyLevelsCache.Clear();
            drawnLevels.Clear();
            sessionTimesCache.Clear();

            // Vyčistíme aj globálnu cache
            lock (lockObject)
            {
                globalDrawnLevels.Clear();
                globalDrawnDates.Clear();
            }

            LogToFile("Trailing variables and drawing cache reset");
        }



        protected override void OnStateChange()
        {
            if (State == State.SetDefaults)
            {
                Name = "CamarillaLiveStrategy";
                Description = "Live trading strategy for Camarilla levels with real-time trailing";
                Calculate = Calculate.OnEachTick; // Use OnEachTick for real-time trailing
                EntriesPerDirection = 1;
                EntryHandling = EntryHandling.AllEntries;
                IsExitOnSessionCloseStrategy = true;
                ExitOnSessionCloseSeconds = 30;
                IsFillLimitOnTouch = false;
                MaximumBarsLookBack = MaximumBarsLookBack.TwoHundredFiftySix;
                OrderFillResolution = OrderFillResolution.High; // PRIDANÉ PRE BACKTEST
                Slippage = 0;
                StartBehavior = StartBehavior.WaitUntilFlat;
                TimeInForce = TimeInForce.Gtc;
                TraceOrders = true;
                RealtimeErrorHandling = RealtimeErrorHandling.StopCancelClose;
                StopTargetHandling = StopTargetHandling.PerEntryExecution;
                BarsRequiredToTrade = 20;

                // Default trail parameters - matched with Pine Script
                LongTrailPoints = 40;
                LongTrailOffset = 1;
                LongStopLoss = 70;
                ShortTrailPoints = 20;
                ShortTrailOffset = 1;
                ShortStopLoss = 40;

                // DÔLEŽITÉ: Pre live trading použite SetTrailStop()
                // Pre backtest trailing stops nefungujú správne

                // Default parameter values
                EmaPeriod = 8;

                // Telegram notification parameters - FILL THESE IN
                TelegramBotToken = ""; // Your bot token here
                TelegramChatId = "";   // Your chat ID here
            }
            else if (State == State.Configure)
            {
                // Add EMA indicator
                ema = EMA(EmaPeriod);
                AddChartIndicator(ema);
            }
            else if (State == State.DataLoaded)
            {
                // OPRAVA: Resetovanie premenných pri načítaní dát
                ResetTrailingVariables();
                strategyStarted = true;
                LogToFile($"Strategy started - Data loaded for {Instrument.FullName}");

                // Strategy loaded and ready - send start notification only once
                if (!startNotificationSent)
                {
                    startNotificationSent = true;
                    stopNotificationSent = false; // Reset stop flag

                    string startMsg = $"🟢 <b>NT STRATEGY STARTED</b>\n" +
                                    $"📊 Strategy: {Name}\n" +
                                    $"📈 Instrument: {Instrument.FullName}\n" +
                                    $"⏰ Time: {DateTime.Now:yyyy-MM-dd HH:mm:ss}";

                    // Send immediately without live trading check for status notifications
                    Task.Run(() => SendTelegramNotificationAsync(startMsg));
                    LogToFile("Strategy started - notification sent");
                }
                else
                {
                    LogToFile("Strategy start notification already sent - skipping");
                }
            }
            else if (State == State.Realtime)
            {
                // Now we're in live trading mode
                isLiveTrading = true;
                LogToFile("Switched to live trading mode - notifications enabled");
            }
            else if (State == State.Terminated)
            {
                // Strategy stopped - send stop notification only once
                if (!stopNotificationSent && startNotificationSent)
                {
                    stopNotificationSent = true;
                    startNotificationSent = false; // Reset start flag for next time

                    string stopMsg = $"🔴 <b>NT STRATEGY STOPPED</b>\n" +
                                   $"📊 Strategy: {Name}\n" +
                                   $"📈 Instrument: {Instrument.FullName}\n" +
                                   $"⏰ Time: {DateTime.Now:yyyy-MM-dd HH:mm:ss}";

                    // Send immediately without live trading check for status notifications
                    Task.Run(() => SendTelegramNotificationAsync(stopMsg));
                    LogToFile("Strategy stopped - notification sent");
                }
                else
                {
                    LogToFile("Strategy stop notification already sent or never started - skipping");
                }

                // Reset live trading flag
                isLiveTrading = false;
            }
        }

        private bool entryEvaluatedThisBar = false;

        // Method to calculate Camarilla levels for a given date
        private void CalculateCamarillaLevels(DateTime currentDate)
        {
            // Initialize variables
            double h4 = 0;
            double l4 = 0;
            bool foundPrevDay = false;
            double prevDayHigh = double.MinValue;
            double prevDayLow = double.MaxValue;
            double prevDayClose = 0;

            // Log the date we're calculating for
            Print($"Calculating Camarilla levels for date: {currentDate:yyyy-MM-dd}, CurrentBar: {CurrentBar}");
            LogToFile($"Calculating Camarilla levels for date: {currentDate:yyyy-MM-dd}, CurrentBar: {CurrentBar}");

            // Debug all available bars
            LogToFile("Debugging all available bars for date calculation:");
            LogToFile($"Bar 0: Time={Time[0]}, Date={Time[0].Date}, Open={Open[0]}, High={High[0]}, Low={Low[0]}, Close={Close[0]}");

            // Určenie predchádzajúceho obchodného dňa podľa CME obchodných hodín
            // Pre CME futures obchodný deň začína o 18:00 predchádzajúceho dňa a končí o 17:00 aktuálneho dňa (NYC čas)
            DateTime prevDate;

            // Určenie predchádzajúceho obchodného dňa podľa tabuľky CME
            switch (currentDate.DayOfWeek)
            {
                case DayOfWeek.Monday:
                    // Pre pondelok - Camarilla úrovne sa počítajú z piatkového denného baru
                    prevDate = currentDate.AddDays(-3); // Piatok
                    break;
                case DayOfWeek.Tuesday:
                    // Pre utorok - Camarilla úrovne sa počítajú z pondelkového denného baru
                    prevDate = currentDate.AddDays(-1); // Pondelok
                    break;
                case DayOfWeek.Wednesday:
                    // Pre stredu - Camarilla úrovne sa počítajú z utorkového denného baru
                    prevDate = currentDate.AddDays(-1); // Utorok
                    break;
                case DayOfWeek.Thursday:
                    // Pre štvrtok - Camarilla úrovne sa počítajú zo stredajšieho denného baru
                    prevDate = currentDate.AddDays(-1); // Streda
                    break;
                case DayOfWeek.Friday:
                    // Pre piatok - Camarilla úrovne sa počítajú zo štvrtkovho denného baru
                    prevDate = currentDate.AddDays(-1); // Štvrtok
                    break;
                default:
                    // Pre víkend (sobota, nedeľa) - neobchodné dni, ale pre istotu
                    prevDate = currentDate.AddDays(-1);
                    if (prevDate.DayOfWeek == DayOfWeek.Saturday)
                        prevDate = currentDate.AddDays(-2); // Piatok
                    else if (prevDate.DayOfWeek == DayOfWeek.Sunday)
                        prevDate = currentDate.AddDays(-3); // Piatok
                    break;
            }

            LogToFile($"CME Trading Day - Current date: {currentDate:yyyy-MM-dd} ({currentDate.DayOfWeek}), Previous trading day: {prevDate:yyyy-MM-dd} ({prevDate.DayOfWeek})");

            // Nájdeme všetky bary pre predchádzajúci obchodný deň
            // Použijeme SessionIterator pre presné určenie obchodných hodín
            var sessionTimes = GetSessionTimes(prevDate);
            DateTime tradingDayStart = sessionTimes.Item1;
            DateTime tradingDayEnd = sessionTimes.Item2;

            LogToFile($"Using SessionIterator for calculation - trading session: {tradingDayStart:yyyy-MM-dd HH:mm:ss} to {tradingDayEnd:yyyy-MM-dd HH:mm:ss}");

            List<int> barsForPrevDay = new List<int>();
            for (int i = 0; i < Math.Min(CurrentBar, 1000); i++)
            {
                DateTime barTime = Time[i];
                // Bar patrí do obchodného dňa ak je medzi 18:00 predchádzajúceho dňa a 17:00 aktuálneho dňa
                if (barTime >= tradingDayStart && barTime <= tradingDayEnd)
                {
                    barsForPrevDay.Add(i);
                    foundPrevDay = true;
                }
            }

            // Ak sme našli bary pre predchádzajúci deň, spracujeme ich
            if (barsForPrevDay.Count > 0)
            {
                // Detailné logovanie všetkých barov pre predchádzajúci deň
                LogToFile($"Found {barsForPrevDay.Count} bars for previous day {prevDate:yyyy-MM-dd}:");
                foreach (int barIndex in barsForPrevDay)
                {
                    LogToFile($"  Bar index {barIndex}: Time={Time[barIndex]}, Open={Open[barIndex]}, High={High[barIndex]}, Low={Low[barIndex]}, Close={Close[barIndex]}");
                }

                // Nájdeme high/low pre všetky bary
                foreach (int barIndex in barsForPrevDay)
                {
                    if (prevDayHigh < High[barIndex])
                    {
                        prevDayHigh = High[barIndex];
                        LogToFile($"New HIGH found: {prevDayHigh} at bar index {barIndex}, Time: {Time[barIndex]}");
                    }
                    if (prevDayLow > Low[barIndex])
                    {
                        prevDayLow = Low[barIndex];
                        LogToFile($"New LOW found: {prevDayLow} at bar index {barIndex}, Time: {Time[barIndex]}");
                    }
                }

                // DÔLEŽITÉ: Close hodnota musí byť z posledného baru obchodného dňa
                // Pre CME futures obchodný deň končí o 17:00 EST (22:00 UTC)
                // Musíme nájsť bar s časom 17:00 (alebo najbližší k tomu)
                int lastBarOfDay = -1;
                DateTime targetEndTime = prevDate.Date.AddHours(17); // 17:00 EST

                // Nájdeme bar s časom 17:00 alebo najbližší k tomu
                foreach (int barIndex in barsForPrevDay)
                {
                    DateTime barTime = Time[barIndex];
                    if (barTime.Hour == 17 && barTime.Minute == 0)
                    {
                        lastBarOfDay = barIndex;
                        break;
                    }
                }

                // Ak sme nenašli presný čas 17:00, vezmeme posledný bar pred 18:00
                if (lastBarOfDay == -1)
                {
                    foreach (int barIndex in barsForPrevDay.OrderBy(x => x))
                    {
                        DateTime barTime = Time[barIndex];
                        if (barTime.Hour < 18)
                        {
                            lastBarOfDay = barIndex;
                        }
                        else
                        {
                            break; // Už sme prešli za 18:00
                        }
                    }
                }

                // Ak stále nemáme posledný bar, vezmeme najstarší bar (najvyšší index)
                if (lastBarOfDay == -1)
                {
                    lastBarOfDay = barsForPrevDay.Max();
                }

                prevDayClose = Close[lastBarOfDay];

                LogToFile($"Last bar of day index: {lastBarOfDay}, Time: {Time[lastBarOfDay]}, Close: {prevDayClose}");
                LogToFile($"All bar indices for previous day: [{string.Join(", ", barsForPrevDay.OrderBy(x => x))}]");
            }

            // Check if we have valid data
            if (foundPrevDay)
            {
                // Log all collected data for the previous day
                LogToFile($"FINAL DATA FOR PREVIOUS DAY: High={prevDayHigh}, Low={prevDayLow}, Close={prevDayClose}");

                // Camarilla formula for H4/L4
                // Správny vzorec pre Camarilla úrovne
                double range = prevDayHigh - prevDayLow;

                // Pôvodný vzorec: H4 = C + (H - L) * 1.1/2, L4 = C - (H - L) * 1.1/2
                double h4Multiplier = 1.1 / 2.0;
                double l4Multiplier = 1.1 / 2.0;

                // Výpočet H4/L4
                h4 = prevDayClose + (range * h4Multiplier);
                l4 = prevDayClose - (range * l4Multiplier);

                // Žiadne korekcie - používame štandardný vzorec Camarilla
                LogToFile($"Using standard Camarilla formula without corrections");

                // Zaokrúhlenie na správny počet desatinných miest podľa nástroja
                int decimalPlaces = GetDecimalPlaces();
                h4 = Math.Round(h4, decimalPlaces);
                l4 = Math.Round(l4, decimalPlaces);

                // Log výpočtových krokov
                LogToFile($"Calculation: Range={range}, H4 Multiplier={h4Multiplier}, L4 Multiplier={l4Multiplier}");
                LogToFile($"H4 = {prevDayClose} + ({range} * {h4Multiplier}) = {h4}");
                LogToFile($"L4 = {prevDayClose} - ({range} * {l4Multiplier}) = {l4}");

                // Log the final values
                LogToFile($"FINAL H4/L4 VALUES: H4={h4}, L4={l4}");

                string calcInfo = $"Calculated H4/L4 for {currentDate:yyyy-MM-dd} based on previous day: H4={h4}, L4={l4}, PrevHigh={prevDayHigh}, PrevLow={prevDayLow}, PrevClose={prevDayClose}";
                Print(calcInfo);
                LogToFile(calcInfo);

                // Cache the calculated values LEN pre aktuálny dátum
                // Úrovne vypočítané z predchádzajúceho dňa sú určené pre aktuálny obchodný deň
                dailyLevelsCache[currentDate] = new Tuple<double, double>(h4, l4);
                LogToFile($"Caching H4/L4 values for current day {currentDate:yyyy-MM-dd}: H4={h4}, L4={l4}");
            }
            else
            {
                // Default values if we can't find previous day's data
                h4 = 5900;
                l4 = 5800;
                string defaultInfo = $"Using default H4/L4 values for {currentDate:yyyy-MM-dd} - could not find previous day's data";
                Print(defaultInfo);
                LogToFile(defaultInfo);

                // Cache the default values LEN pre aktuálny dátum
                dailyLevelsCache[currentDate] = new Tuple<double, double>(h4, l4);
                LogToFile($"Caching default H4/L4 values for current day {currentDate:yyyy-MM-dd}: H4={h4}, L4={l4}");
            }
        }

        protected override void OnBarUpdate()
        {
            try
            {
                // Skip processing if we don't have enough bars - REDUCED TO MINIMUM
                if (BarsInProgress != 0 || CurrentBar < 1) // Only need 1 bar
                    return;

                // Debug info - print every bar
                if (IsFirstTickOfBar)
                {
                    string barInfo = $"Processing bar: {CurrentBar}, Time: {Time[0]}, Position: {Position.MarketPosition}";
                    Print(barInfo);
                    LogToFile(barInfo);
                    // Reset entry flag at the start of a new bar
                    entryEvaluatedThisBar = false;
                }

                // Get current bar date
                DateTime currentDate = Time[0].Date;

                // Check if we already calculated H4/L4 for this date
                if (!dailyLevelsCache.ContainsKey(currentDate))
                {
                    // Calculate H4/L4 values dynamically from previous day's data
                    CalculateCamarillaLevels(currentDate);
                }
                else
                {
                    // Use cached values
                    var levels = dailyLevelsCache[currentDate];
                    h4 = levels.Item1;
                    l4 = levels.Item2;
                }

                // Draw H4/L4 levels LEN pre posledný deň v dátach aby sa zabránilo duplicitným levelom
                // OPRAVA: Kreslíme len ak je to posledný dostupný deň (pondelok 26.5.2025)
                bool isLastDay = currentDate.Date == new DateTime(2025, 5, 26).Date;

                if (IsFirstTickOfBar && (CurrentBar == 0 || Time[1].Date != currentDate) && isLastDay)
                {
                    LogToFile($"DRAWING LEVELS: Date={currentDate:yyyy-MM-dd}, CurrentBar={CurrentBar}, Count={Count}, IsLastDay={isLastDay}");
                    string dateStr = currentDate.ToString("yyyyMMdd");
                    string instrumentKey = Instrument.FullName.Replace(" ", "").Replace("/", ""); // Clean instrument name

                    // OPRAVA: Použijeme unikátne kľúče pre každý nástroj
                    string h4Key = $"H4_{dateStr}_{instrumentKey}";
                    string l4Key = $"L4_{dateStr}_{instrumentKey}";

                    // OPRAVA: Odstránime všetky existujúce čiary pred kreslením nových
                    RemoveDrawObject(h4Key);
                    RemoveDrawObject(l4Key);
                    RemoveDrawObject($"H4_Text_{dateStr}_{instrumentKey}");
                    RemoveDrawObject($"L4_Text_{dateStr}_{instrumentKey}");

                    // OPRAVA: Používame hodnoty pre aktuálny deň, ktoré sú už vypočítané z predchádzajúceho dňa
                    // Tieto hodnoty sú už správne, pretože sme ich vypočítali z predchádzajúceho dňa
                    double currentH4 = h4;
                    double currentL4 = l4;

                    // Získame hodnoty z cache pre aktuálny deň
                    if (dailyLevelsCache.ContainsKey(currentDate))
                    {
                        var currentLevels = dailyLevelsCache[currentDate];
                        currentH4 = currentLevels.Item1;
                        currentL4 = currentLevels.Item2;
                    }

                    LogToFile($"Using H4/L4 values for current day {currentDate:yyyy-MM-dd}: H4={currentH4}, L4={currentL4}");

                    // OPRAVA: Globálna kontrola - kreslíme len raz pre každý deň a nástroj
                    string levelKey = $"{dateStr}_{instrumentKey}";

                    lock (lockObject)
                    {
                        if (!globalDrawnLevels.Contains(levelKey))
                        {
                            globalDrawnLevels.Add(levelKey);
                            drawnLevels.Add(levelKey);

                        // Get trading session boundaries using SessionIterator
                        var sessionTimes = GetSessionTimes(currentDate);
                        DateTime sessionStart = sessionTimes.Item1;
                        DateTime sessionEnd = sessionTimes.Item2;

                        LogToFile($"Drawing H4/L4 lines using SessionIterator times: {sessionStart:yyyy-MM-dd HH:mm:ss} to {sessionEnd:yyyy-MM-dd HH:mm:ss}");

                        // OPRAVA: Kreslenie s unikátnymi kľúčmi pre každý nástroj
                        Draw.Line(this, h4Key, false, sessionStart, currentH4, sessionEnd, currentH4, Brushes.Orange, DashStyleHelper.Solid, 2);
                        Draw.Line(this, l4Key, false, sessionStart, currentL4, sessionEnd, currentL4, Brushes.Orange, DashStyleHelper.Solid, 2);

                        // Add text labels with appropriate decimal places
                        int decimalPlaces = GetDecimalPlaces();
                        string formatString = "0." + new string('0', decimalPlaces);
                        Draw.Text(this, $"H4_Text_{dateStr}_{instrumentKey}", "H4: " + currentH4.ToString(formatString), 0, currentH4 + 5 * TickSize, Brushes.Orange);
                        Draw.Text(this, $"L4_Text_{dateStr}_{instrumentKey}", "L4: " + currentL4.ToString(formatString), 0, currentL4 - 5 * TickSize, Brushes.Orange);

                            // Log the drawing of lines
                            LogToFile($"Drawing H4/L4 lines for {currentDate:yyyy-MM-dd}: H4={currentH4}, L4={currentL4}");
                            LogToFile($"Session boundaries: Start={sessionStart:yyyy-MM-dd HH:mm:ss}, End={sessionEnd:yyyy-MM-dd HH:mm:ss}");
                        }
                        else
                        {
                            LogToFile($"Levels for {currentDate:yyyy-MM-dd} already drawn globally - skipping");
                        }
                    }
                }
                else if (IsFirstTickOfBar && (CurrentBar == 0 || Time[1].Date != currentDate))
                {
                    LogToFile($"SKIPPING LEVELS: Date={currentDate:yyyy-MM-dd}, CurrentBar={CurrentBar}, Count={Count}, IsLastDay={isLastDay} - Not last day (26.5.2025)");
                }

                // CAMARILLA ENTRY CONDITIONS
                // Potrebujeme použiť hodnoty z predchádzajúceho dňa pre vstupné podmienky
                double entryH4 = h4;
                double entryL4 = l4;

                // Určenie predchádzajúceho obchodného dňa podľa CME obchodných hodín
                // Pre CME futures obchodný deň začína o 18:00 predchádzajúceho dňa a končí o 17:00 aktuálneho dňa (NYC čas)
                DateTime prevDate;

                // Určenie predchádzajúceho obchodného dňa podľa tabuľky CME
                switch (currentDate.DayOfWeek)
                {
                    case DayOfWeek.Monday:
                        // Pre pondelok - Camarilla úrovne sa počítajú z piatkového denného baru
                        prevDate = currentDate.AddDays(-3); // Piatok
                        break;
                    case DayOfWeek.Tuesday:
                        // Pre utorok - Camarilla úrovne sa počítajú z pondelkového denného baru
                        prevDate = currentDate.AddDays(-1); // Pondelok
                        break;
                    case DayOfWeek.Wednesday:
                        // Pre stredu - Camarilla úrovne sa počítajú z utorkového denného baru
                        prevDate = currentDate.AddDays(-1); // Utorok
                        break;
                    case DayOfWeek.Thursday:
                        // Pre štvrtok - Camarilla úrovne sa počítajú zo stredajšieho denného baru
                        prevDate = currentDate.AddDays(-1); // Streda
                        break;
                    case DayOfWeek.Friday:
                        // Pre piatok - Camarilla úrovne sa počítajú zo štvrtkovho denného baru
                        prevDate = currentDate.AddDays(-1); // Štvrtok
                        break;
                    default:
                        // Pre víkend (sobota, nedeľa) - neobchodné dni, ale pre istotu
                        prevDate = currentDate.AddDays(-1);
                        if (prevDate.DayOfWeek == DayOfWeek.Saturday)
                            prevDate = currentDate.AddDays(-2); // Piatok
                        else if (prevDate.DayOfWeek == DayOfWeek.Sunday)
                            prevDate = currentDate.AddDays(-3); // Piatok
                        break;
                }

                // Logujeme informácie o vstupných podmienkach len pri prvom ticku baru alebo pri zmene dňa
                if (IsFirstTickOfBar || (CurrentBar > 0 && Time[1].Date != currentDate))
                {
                    LogToFile($"Entry conditions - Current date: {currentDate:yyyy-MM-dd} ({currentDate.DayOfWeek}), Previous trading day: {prevDate:yyyy-MM-dd} ({prevDate.DayOfWeek})");
                }

                // OPRAVA: Pre entry používame TIE ISTÉ H4/L4 hodnoty, ktoré sa kreslia na graf
                // To znamená H4/L4 hodnoty vypočítané pre aktuálny deň (z predchádzajúceho dňa)
                if (dailyLevelsCache.ContainsKey(currentDate))
                {
                    var currentLevels = dailyLevelsCache[currentDate];
                    entryH4 = currentLevels.Item1;
                    entryL4 = currentLevels.Item2;

                    // Logujeme informácie o H4/L4 hodnotách len pri prvom ticku baru alebo pri zmene dňa
                    if (IsFirstTickOfBar || (CurrentBar > 0 && Time[1].Date != currentDate))
                    {
                        LogToFile($"Using entry H4/L4 values for current day {currentDate:yyyy-MM-dd}: H4={entryH4}, L4={entryL4} (same as drawn levels)");
                    }
                }

                // Long entry: Price crosses above H4
                bool longCondition1 = CrossAbove(Close, entryH4, 1);
                bool longCondition2 = Close[0] > entryH4;
                bool longCondition3 = Position.MarketPosition == MarketPosition.Flat;

                // Short entry: Price crosses below L4
                bool shortCondition1 = CrossBelow(Close, entryL4, 1);
                bool shortCondition2 = Close[0] < entryL4;
                bool shortCondition3 = Position.MarketPosition == MarketPosition.Flat;

                // OPRAVA: Kontrola existujúcich pozícií pri spustení stratégie
                if (Position.MarketPosition != MarketPosition.Flat && !existingPositionProcessed && strategyStarted)
                {
                    // Našli sme existujúcu pozíciu - inicializujeme premenné ale neposielame entry notifikácie
                    if (Position.MarketPosition == MarketPosition.Long)
                    {
                        entryPrice = Position.AveragePrice;
                        highestSinceEntry = entryPrice;
                        longTrailingActive = false;
                        currentTrailStop = 0;
                        isNewPosition = false; // OPRAVA: Existujúca pozícia NIE JE nová
                        LogToFile($"EXISTING LONG POSITION DETECTED: Entry={entryPrice}, Quantity={Position.Quantity} - TRAILING DISABLED");
                    }
                    else if (Position.MarketPosition == MarketPosition.Short)
                    {
                        entryPrice = Position.AveragePrice;
                        lowestSinceEntry = entryPrice;
                        shortTrailingActive = false;
                        currentTrailStop = 0;
                        isNewPosition = false; // OPRAVA: Existujúca pozícia NIE JE nová
                        LogToFile($"EXISTING SHORT POSITION DETECTED: Entry={entryPrice}, Quantity={Position.Quantity} - TRAILING DISABLED");
                    }
                    existingPositionProcessed = true; // Označíme, že sme už spracovali existujúcu pozíciu
                }

                // Entry logic - ONLY EVALUATE ON BAR CLOSE
                // This ensures entries happen like in TradingView (only at bar close)
                if (!entryEvaluatedThisBar && Calculate == Calculate.OnBarClose ||
                    !entryEvaluatedThisBar && Calculate == Calculate.OnEachTick && IsFirstTickOfBar)
                {
                    // Long entry
                    if (longCondition1 && longCondition2 && longCondition3)
                    {
                        EnterLong(1, "Long");
                        entryPrice = Close[0];
                        highestSinceEntry = Close[0];
                        longTrailingActive = false;
                        currentTrailStop = 0; // Reset trailing stop
                        isNewPosition = true; // OPRAVA: Označíme ako novú pozíciu
                        Draw.ArrowUp(this, "LongEntry_" + CurrentBar, true, 0, Low[0] - 10 * TickSize, Brushes.Green);
                        Draw.Text(this, "LongEntryText_" + CurrentBar, "LONG", 0, Low[0] - 20 * TickSize, Brushes.Green);
                        Print($"LONG ENTRY: {Instrument.FullName} at {Close[0]}, Time: {Time[0]}, Bar: {CurrentBar}");
                        LogToFile($"NEW LONG ENTRY: Price={Close[0]}, Time={Time[0]}, Bar={CurrentBar}");

                        // TELEGRAM NOTIFICATION - LONG ENTRY
                        double stopLossPrice = entryPrice - (LongStopLoss * TickSize);
                        double trailActivationPrice = entryPrice + (LongTrailPoints * TickSize);
                        string telegramMsg = $"🟢 <b>NT LONG ENTRY</b>\n" +
                                           $"📊 Instrument: {Instrument.FullName}\n" +
                                           $"💰 Entry Price: {entryPrice:F2}\n" +
                                           $"📉 Stop Loss: {stopLossPrice:F2}\n" +
                                           $"🎯 Trail Activation: {trailActivationPrice:F2}\n" +
                                           $"📦 Quantity: 1\n" +
                                           $"⏰ Time: {Time[0]:yyyy-MM-dd HH:mm:ss}";
                        SendTelegramNotification(telegramMsg);
                    }
                    // Short entry
                    else if (shortCondition1 && shortCondition2 && shortCondition3)
                    {
                        EnterShort(1, "Short");
                        entryPrice = Close[0];
                        lowestSinceEntry = Close[0];
                        shortTrailingActive = false;
                        currentTrailStop = 0; // Reset trailing stop
                        isNewPosition = true; // OPRAVA: Označíme ako novú pozíciu
                        Draw.ArrowDown(this, "ShortEntry_" + CurrentBar, true, 0, High[0] + 10 * TickSize, Brushes.Red);
                        Draw.Text(this, "ShortEntryText_" + CurrentBar, "SHORT", 0, High[0] + 20 * TickSize, Brushes.Red);
                        Print($"SHORT ENTRY: {Instrument.FullName} at {Close[0]}, Time: {Time[0]}, Bar: {CurrentBar}");
                        LogToFile($"NEW SHORT ENTRY: Price={Close[0]}, Time={Time[0]}, Bar={CurrentBar}");

                        // TELEGRAM NOTIFICATION - SHORT ENTRY
                        double stopLossPrice = entryPrice + (ShortStopLoss * TickSize);
                        double trailActivationPrice = entryPrice - (ShortTrailPoints * TickSize);
                        string telegramMsg = $"🔴 <b>NT SHORT ENTRY</b>\n" +
                                           $"📊 Instrument: {Instrument.FullName}\n" +
                                           $"💰 Entry Price: {entryPrice:F2}\n" +
                                           $"📈 Stop Loss: {stopLossPrice:F2}\n" +
                                           $"🎯 Trail Activation: {trailActivationPrice:F2}\n" +
                                           $"📦 Quantity: 1\n" +
                                           $"⏰ Time: {Time[0]:yyyy-MM-dd HH:mm:ss}";
                        SendTelegramNotification(telegramMsg);
                    }

                    // Mark that we've evaluated entries for this bar
                    entryEvaluatedThisBar = true;
                }

                // Trailing stop logic - MUSÍ SA VYHODNOCOVAŤ NA KAŽDOM TICKU, NIE LEN PRI BAR CLOSE
                // Manage trailing stop for long
                if (Position.MarketPosition == MarketPosition.Long)
                {
                    // Update highest price since entry - kontrolujeme aj High[0] a aktuálnu cenu
                    double currentHigh = Math.Max(High[0], Close[0]);
                    if (currentHigh > highestSinceEntry)
                    {
                        highestSinceEntry = currentHigh;
                        LogToFile($"LONG: Updated highest since entry to {highestSinceEntry}, Current price: {Close[0]}, High[0]: {High[0]}");
                    }

                    // Activate trailing stop only after price moves LongTrailPoints ticks in our favor
                    double trailActivationPrice = entryPrice + (LongTrailPoints * TickSize);

                    if (!longTrailingActive)
                    {
                        // OPRAVA: Logujeme len občas, nie na každom ticku
                        if (IsFirstTickOfBar || !isLiveTrading)
                        {
                            LogToFile($"LONG TRAIL CHECK: Entry={entryPrice}, Current={Close[0]}, High={highestSinceEntry}, Activation needed={trailActivationPrice}, Points={LongTrailPoints}");
                        }

                        // OPRAVA: Aktivujeme trailing stop len ak je pozícia nová (nie existujúca)
                        if ((Close[0] >= trailActivationPrice || High[0] >= trailActivationPrice) && isNewPosition)
                        {
                            longTrailingActive = true;
                            string activationMsg = $"LONG TRAIL ACTIVATED: Price={Close[0]}, High={High[0]}, Entry={entryPrice}, Activation={trailActivationPrice}, Points={LongTrailPoints} ticks";
                            Print(activationMsg);
                            LogToFile(activationMsg);

                            // TELEGRAM NOTIFICATION - LONG TRAIL ACTIVATED
                            string telegramMsg = $"🎯 <b>NT LONG TRAIL ACTIVATED</b>\n" +
                                               $"📊 Instrument: {Instrument.FullName}\n" +
                                               $"💰 Current Price: {Close[0]:F2}\n" +
                                               $"🚀 Entry Price: {entryPrice:F2}\n" +
                                               $"📈 Activation Level: {trailActivationPrice:F2}\n" +
                                               $"🔄 Trail Offset: {LongTrailOffset} ticks\n" +
                                               $"⏰ Time: {Time[0]:yyyy-MM-dd HH:mm:ss}";
                            SendTelegramNotification(telegramMsg);
                        }
                    }

                    // If trailing stop is active, manage trailing stop
                    if (longTrailingActive)
                    {
                        double newStop = highestSinceEntry - (LongTrailOffset * TickSize);

                        // Aktualizujeme trailing stop len ak sa zlepšil (posunul vyššie)
                        if (newStop > currentTrailStop || currentTrailStop == 0)
                        {
                            currentTrailStop = newStop;

                            // POUŽIJEME SETSTOPLOSS PRE OKAMŽITÚ AKTUALIZÁCIU
                            SetStopLoss("Long", CalculationMode.Price, currentTrailStop, false);

                            LogToFile($"LONG TRAIL UPDATED: New stop={currentTrailStop}, Highest={highestSinceEntry}, Offset={LongTrailOffset}, Current={Close[0]}");

                            // Draw trailing stop line
                            if (IsFirstTickOfBar)
                                Draw.Line(this, "LongTrailStop", false, 0, currentTrailStop, 10, currentTrailStop, Brushes.Blue, DashStyleHelper.Dash, 1);
                        }

                        // OPRAVA: Logujeme len občas, nie na každom ticku
                        if (IsFirstTickOfBar || !isLiveTrading)
                        {
                            LogToFile($"LONG TRAIL CHECK: Current={Close[0]}, TrailStop={currentTrailStop}, Should exit={Close[0] <= currentTrailStop}");
                        }

                        // VLASTNÝ EXIT MECHANIZMUS PRE BACKTEST
                        if (Close[0] <= currentTrailStop)
                        {
                            ExitLong("LongTrail", "Long");
                            double profitTicks = (Close[0] - entryPrice) / TickSize;
                            double profitUSD = profitTicks * Instrument.MasterInstrument.PointValue;
                            string exitMsg = $"LONG TRAIL EXIT: Price={Close[0]}, TrailStop={currentTrailStop}, Highest={highestSinceEntry}, Profit={profitTicks:F1} ticks";
                            Print(exitMsg);
                            LogToFile(exitMsg);

                            // TELEGRAM NOTIFICATION - LONG TRAIL EXIT
                            string telegramMsg = $"🟢 <b>NT LONG TRAIL EXIT</b>\n" +
                                               $"📊 Instrument: {Instrument.FullName}\n" +
                                               $"💰 Exit Price: {Close[0]:F2}\n" +
                                               $"🚀 Entry Price: {entryPrice:F2}\n" +
                                               $"🛑 Trail Stop: {currentTrailStop:F2}\n" +
                                               $"📈 Profit: {profitTicks:F1} ticks (${profitUSD:F2})\n" +
                                               $"📦 Quantity: 1\n" +
                                               $"⏰ Time: {Time[0]:yyyy-MM-dd HH:mm:ss}";
                            SendTelegramNotification(telegramMsg);

                            // Reset trailing stop
                            currentTrailStop = 0;
                            longTrailingActive = false;
                            isNewPosition = false; // OPRAVA: Reset pre ďalšie pozície
                        }
                    }
                    else
                    {
                        // Fixed stop loss - len ak trailing nie je aktívny
                        double stopPrice = entryPrice - (LongStopLoss * TickSize);
                        SetStopLoss("Long", CalculationMode.Price, stopPrice, false);

                        // Draw stop loss line - only once when position is opened
                        if (IsFirstTickOfBar && BarsSinceEntryExecution() == 0)
                            Draw.Line(this, "LongStopLoss", false, 0, stopPrice, 10, stopPrice, Brushes.Red, DashStyleHelper.Dash, 1);
                    }
                }

                // Manage trailing stop for short
                if (Position.MarketPosition == MarketPosition.Short)
                {
                    // Update lowest price since entry - kontrolujeme aj Low[0] a aktuálnu cenu
                    double currentLow = Math.Min(Low[0], Close[0]);
                    if (currentLow < lowestSinceEntry)
                    {
                        lowestSinceEntry = currentLow;
                        LogToFile($"SHORT: Updated lowest since entry to {lowestSinceEntry}, Current price: {Close[0]}, Low[0]: {Low[0]}");
                    }

                    // Activate trailing stop only after price moves ShortTrailPoints ticks in our favor
                    double trailActivationPrice = entryPrice - (ShortTrailPoints * TickSize);

                    if (!shortTrailingActive)
                    {
                        // OPRAVA: Logujeme len občas, nie na každom ticku
                        if (IsFirstTickOfBar || !isLiveTrading)
                        {
                            LogToFile($"SHORT TRAIL CHECK: Entry={entryPrice}, Current={Close[0]}, Low={lowestSinceEntry}, Activation needed={trailActivationPrice}, Points={ShortTrailPoints}");
                        }

                        // OPRAVA: Aktivujeme trailing stop len ak je pozícia nová (nie existujúca)
                        if ((Close[0] <= trailActivationPrice || Low[0] <= trailActivationPrice) && isNewPosition)
                        {
                            shortTrailingActive = true;
                            string activationMsg = $"SHORT TRAIL ACTIVATED: Price={Close[0]}, Low={Low[0]}, Entry={entryPrice}, Activation={trailActivationPrice}, Points={ShortTrailPoints} ticks";
                            Print(activationMsg);
                            LogToFile(activationMsg);

                            // TELEGRAM NOTIFICATION - SHORT TRAIL ACTIVATED
                            string telegramMsg = $"🎯 <b>NT SHORT TRAIL ACTIVATED</b>\n" +
                                               $"📊 Instrument: {Instrument.FullName}\n" +
                                               $"💰 Current Price: {Close[0]:F2}\n" +
                                               $"🚀 Entry Price: {entryPrice:F2}\n" +
                                               $"📉 Activation Level: {trailActivationPrice:F2}\n" +
                                               $"🔄 Trail Offset: {ShortTrailOffset} ticks\n" +
                                               $"⏰ Time: {Time[0]:yyyy-MM-dd HH:mm:ss}";
                            SendTelegramNotification(telegramMsg);
                        }
                    }

                    // If trailing stop is active, manage trailing stop
                    if (shortTrailingActive)
                    {
                        double newStop = lowestSinceEntry + (ShortTrailOffset * TickSize);

                        // Aktualizujeme trailing stop len ak sa zlepšil (posunul nižšie)
                        if (newStop < currentTrailStop || currentTrailStop == 0)
                        {
                            currentTrailStop = newStop;

                            // POUŽIJEME SETSTOPLOSS PRE OKAMŽITÚ AKTUALIZÁCIU
                            SetStopLoss("Short", CalculationMode.Price, currentTrailStop, false);

                            LogToFile($"SHORT TRAIL UPDATED: New stop={currentTrailStop}, Lowest={lowestSinceEntry}, Offset={ShortTrailOffset}, Current={Close[0]}");

                            // Draw trailing stop line
                            if (IsFirstTickOfBar)
                                Draw.Line(this, "ShortTrailStop", false, 0, currentTrailStop, 10, currentTrailStop, Brushes.Blue, DashStyleHelper.Dash, 1);
                        }

                        // OPRAVA: Logujeme len občas, nie na každom ticku
                        if (IsFirstTickOfBar || !isLiveTrading)
                        {
                            LogToFile($"SHORT TRAIL CHECK: Current={Close[0]}, TrailStop={currentTrailStop}, Should exit={Close[0] >= currentTrailStop}");
                        }

                        // VLASTNÝ EXIT MECHANIZMUS PRE BACKTEST
                        if (Close[0] >= currentTrailStop)
                        {
                            ExitShort("ShortTrail", "Short");
                            double profitTicks = (entryPrice - Close[0]) / TickSize;
                            double profitUSD = profitTicks * Instrument.MasterInstrument.PointValue;
                            string exitMsg = $"SHORT TRAIL EXIT: Price={Close[0]}, TrailStop={currentTrailStop}, Lowest={lowestSinceEntry}, Profit={profitTicks:F1} ticks";
                            Print(exitMsg);
                            LogToFile(exitMsg);

                            // TELEGRAM NOTIFICATION - SHORT TRAIL EXIT
                            string telegramMsg = $"🔴 <b>NT SHORT TRAIL EXIT</b>\n" +
                                               $"📊 Instrument: {Instrument.FullName}\n" +
                                               $"💰 Exit Price: {Close[0]:F2}\n" +
                                               $"🚀 Entry Price: {entryPrice:F2}\n" +
                                               $"🛑 Trail Stop: {currentTrailStop:F2}\n" +
                                               $"📈 Profit: {profitTicks:F1} ticks (${profitUSD:F2})\n" +
                                               $"📦 Quantity: 1\n" +
                                               $"⏰ Time: {Time[0]:yyyy-MM-dd HH:mm:ss}";
                            SendTelegramNotification(telegramMsg);

                            // Reset trailing stop
                            currentTrailStop = 0;
                            shortTrailingActive = false;
                            isNewPosition = false; // OPRAVA: Reset pre ďalšie pozície
                        }
                    }
                    else
                    {
                        // Fixed stop loss - len ak trailing nie je aktívny
                        double stopPrice = entryPrice + (ShortStopLoss * TickSize);
                        SetStopLoss("Short", CalculationMode.Price, stopPrice, false);

                        // Draw stop loss line - only once when position is opened
                        if (IsFirstTickOfBar && BarsSinceEntryExecution() == 0)
                            Draw.Line(this, "ShortStopLoss", false, 0, stopPrice, 10, stopPrice, Brushes.Red, DashStyleHelper.Dash, 1);
                    }
                }
            }
            catch (Exception ex)
            {
                Print($"Error in OnBarUpdate: {ex.Message}");
            }
        }

        // Override OnExecutionUpdate to catch stop loss exits
        protected override void OnExecutionUpdate(Execution execution, string executionId, double price, int quantity, MarketPosition marketPosition, string orderId, DateTime time)
        {
            try
            {
                // Check if this is a stop loss exit
                if (execution.Order != null && execution.Order.OrderState == OrderState.Filled)
                {
                    string orderName = execution.Order.Name;

                    // Check for fixed stop loss exits
                    if (orderName.Contains("Stop loss") || orderName.Contains("StopLoss"))
                    {
                        double profitTicks = 0;
                        string direction = "";

                        if (marketPosition == MarketPosition.Flat && execution.Order.OrderAction == OrderAction.Sell)
                        {
                            // Long position closed by stop loss
                            direction = "LONG";
                            profitTicks = (price - entryPrice) / TickSize;
                        }
                        else if (marketPosition == MarketPosition.Flat && execution.Order.OrderAction == OrderAction.BuyToCover)
                        {
                            // Short position closed by stop loss
                            direction = "SHORT";
                            profitTicks = (entryPrice - price) / TickSize;
                        }

                        if (!string.IsNullOrEmpty(direction))
                        {
                            // TELEGRAM NOTIFICATION - FIXED STOP LOSS EXIT
                            double lossUSD = Math.Abs(profitTicks) * Instrument.MasterInstrument.PointValue;
                            string emoji = direction == "LONG" ? "🟢" : "🔴";
                            string telegramMsg = $"{emoji} <b>NT {direction} STOP LOSS EXIT</b>\n" +
                                               $"📊 Instrument: {Instrument.FullName}\n" +
                                               $"💰 Exit Price: {price:F2}\n" +
                                               $"🚀 Entry Price: {entryPrice:F2}\n" +
                                               $"🛑 Stop Loss Hit\n" +
                                               $"📉 Loss: {profitTicks:F1} ticks (${lossUSD:F2})\n" +
                                               $"📦 Quantity: {quantity}\n" +
                                               $"⏰ Time: {time:yyyy-MM-dd HH:mm:ss}";
                            SendTelegramNotification(telegramMsg);

                            LogToFile($"{direction} STOP LOSS EXIT: Price={price}, Entry={entryPrice}, Loss={profitTicks} ticks (${lossUSD:F2})");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                LogToFile($"Error in OnExecutionUpdate: {ex.Message}");
            }
        }

        #region Properties
        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name="EMA Period", Description="Period for EMA indicator", Order=1, GroupName="Parameters")]
        public int EmaPeriod
        { get; set; }

        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name="Long Trail Points", Description="Trail points for long positions", Order=2, GroupName="Parameters")]
        public int LongTrailPoints
        { get; set; }

        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name="Long Trail Offset", Description="Trail offset for long positions", Order=3, GroupName="Parameters")]
        public int LongTrailOffset
        { get; set; }

        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name="Long Stop Loss", Description="Stop loss for long positions", Order=4, GroupName="Parameters")]
        public int LongStopLoss
        { get; set; }

        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name="Short Trail Points", Description="Trail points for short positions", Order=5, GroupName="Parameters")]
        public int ShortTrailPoints
        { get; set; }

        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name="Short Trail Offset", Description="Trail offset for short positions", Order=6, GroupName="Parameters")]
        public int ShortTrailOffset
        { get; set; }

        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name="Short Stop Loss", Description="Stop loss for short positions", Order=7, GroupName="Parameters")]
        public int ShortStopLoss
        { get; set; }

        [NinjaScriptProperty]
        [Display(Name="Telegram Bot Token", Description="Telegram bot token for notifications", Order=8, GroupName="Telegram")]
        public string TelegramBotToken
        { get; set; }

        [NinjaScriptProperty]
        [Display(Name="Telegram Chat ID", Description="Telegram chat ID for notifications", Order=9, GroupName="Telegram")]
        public string TelegramChatId
        { get; set; }
        #endregion
    }
}
