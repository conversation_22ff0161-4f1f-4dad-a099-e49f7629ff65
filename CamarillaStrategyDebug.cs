#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.Indicators;
using NinjaTrader.NinjaScript.DrawingTools;
#endregion

//This namespace holds Strategies in this folder and is required. Do not change it. 
namespace NinjaTrader.NinjaScript.Strategies
{
    public class CamarillaStrategy : Strategy
    {
        private EMA ema;
        private double h4;
        private double l4;
        private double entryPrice;
        private double highestSinceEntry;
        private double lowestSinceEntry;
        private bool longTrailingActive;
        private bool shortTrailingActive;
        private bool isInLongPosition;
        private bool isInShortPosition;
        private double pointSize; // Size of 1 point in ticks
        
        // Store previous day's values
        private double prevDayHigh;
        private double prevDayLow;
        private double prevDayClose;
        private DateTime prevDayDate;
        
        // Debug flags
        private bool debugMode = true;
        private int debugBarCount = 0;

        protected override void OnStateChange()
        {
            if (State == State.SetDefaults)
            {
                Name = "CamarillaStrategy";
                Description = "Camarilla Strategy based on H4/L4 levels with custom trailing stop";
                Calculate = Calculate.OnBarClose; // Changed to OnBarClose to match TradingView
                IsOverlay = true;
                
                // Default parameters - matching TradingView pine script
                // These are in price points, not ticks
                EmaPeriod = 8;
                LongTrailPoints = 60;
                LongTrailOffset = 1;
                LongStopLoss = 100;
                ShortTrailPoints = 40;
                ShortTrailOffset = 1;
                ShortStopLoss = 70;
                
                // Initialize variables
                isInLongPosition = false;
                isInShortPosition = false;
                longTrailingActive = false;
                shortTrailingActive = false;
                
                // Initialize previous day values
                prevDayHigh = 0;
                prevDayLow = 0;
                prevDayClose = 0;
                prevDayDate = DateTime.MinValue;
            }
            else if (State == State.DataLoaded)
            {
                ema = EMA(EmaPeriod);
                
                // Determine point size based on instrument
                // For MES, 1 point = 4 ticks (0.25 index points)
                // For ES, 1 point = 4 ticks (1.00 index points)
                // For MGC, 1 point = 10 ticks (0.1 troy ounce)
                if (Instrument.MasterInstrument.Name.Contains("MES"))
                    pointSize = 4; // 1 point = 4 ticks for MES
                else if (Instrument.MasterInstrument.Name.Contains("ES"))
                    pointSize = 4; // 1 point = 4 ticks for ES
                else if (Instrument.MasterInstrument.Name.Contains("MGC"))
                    pointSize = 10; // 1 point = 10 ticks for MGC
                else
                    pointSize = 1; // Default: assume 1 point = 1 tick
                
                Print($"Instrument: {Instrument.MasterInstrument.Name}, Point Size: {pointSize} ticks");
                Print($"Strategy initialized with Calculate = {Calculate}");
            }
        }

        protected override void OnBarUpdate()
        {
            // Make sure we have enough bars to work with
            if (CurrentBar < 20)
                return;
            
            // Debug: Print every 20 bars to avoid flooding the output
            debugBarCount++;
            bool shouldPrintDebug = debugMode && (debugBarCount % 20 == 0 || IsFirstTickOfBar);
            
            // Check if we have a new day
            DateTime currentBarDate = Time[0].Date;
            
            // If this is the first bar of a new day, update previous day's values
            if (currentBarDate != prevDayDate && prevDayDate != DateTime.MinValue)
            {
                // Find the previous day's high, low, and close
                double dayHigh = High[1];
                double dayLow = Low[1];
                double dayClose = Close[1];
                
                // Store for next day
                prevDayHigh = dayHigh;
                prevDayLow = dayLow;
                prevDayClose = dayClose;
                
                Print($"New day detected: {currentBarDate}, Previous day: {prevDayDate}");
                Print($"Previous day's High: {prevDayHigh}, Low: {prevDayLow}, Close: {prevDayClose}");
            }
            else if (prevDayDate == DateTime.MinValue)
            {
                // Initialize with the first bar's data
                prevDayHigh = High[1];
                prevDayLow = Low[1];
                prevDayClose = Close[1];
                Print($"Initializing previous day's data: High: {prevDayHigh}, Low: {prevDayLow}, Close: {prevDayClose}");
            }
            
            // Store current date for next bar
            prevDayDate = currentBarDate;
            
            // Skip if we don't have previous day's data yet
            if (prevDayHigh == 0 || prevDayLow == 0 || prevDayClose == 0)
            {
                if (shouldPrintDebug)
                    Print($"Skipping bar at {Time[0]} - missing previous day's data");
                return;
            }
            
            // Calculate H4 and L4 exactly as in pine script
            h4 = prevDayClose + (prevDayHigh - prevDayLow) * 1.1 / 2.0;
            l4 = prevDayClose - (prevDayHigh - prevDayLow) * 1.1 / 2.0;
            
            // Debug output to verify calculations
            if (shouldPrintDebug)
            {
                Print($"Bar: {Time[0]}, Open: {Open[0]}, High: {High[0]}, Low: {Low[0]}, Close: {Close[0]}");
                Print($"Prev Day High: {prevDayHigh}, Prev Day Low: {prevDayLow}, Prev Day Close: {prevDayClose}");
                Print($"H4: {h4}, L4: {l4}, EMA: {ema[0]}");
                
                // Check entry conditions
                bool longCondition1 = Close[0] > h4;
                bool longCondition2 = Open[0] < h4;
                bool longCondition3 = ema[0] < Close[0];
                bool shortCondition1 = Close[0] < l4;
                bool shortCondition2 = Open[0] > l4;
                bool shortCondition3 = ema[0] > Close[0];
                
                Print($"Long Conditions: Close > H4: {longCondition1}, Open < H4: {longCondition2}, EMA < Close: {longCondition3}");
                Print($"Short Conditions: Close < L4: {shortCondition1}, Open > L4: {shortCondition2}, EMA > Close: {shortCondition3}");
            }

            // Entry signals
            // Long entry condition - matching TradingView pine script exactly
            if (Close[0] > h4 && Open[0] < h4 && ema[0] < Close[0] && Position.MarketPosition == MarketPosition.Flat)
            {
                EnterLong(1, "Long");
                entryPrice = Close[0];
                highestSinceEntry = Close[0];
                isInLongPosition = true;
                isInShortPosition = false;
                longTrailingActive = false;
                
                // Log entry
                Print($"LONG ENTRY: {Instrument.FullName} at {Close[0]}, Time: {Time[0]}");
                Print($"Entry Conditions: Close({Close[0]}) > H4({h4}), Open({Open[0]}) < H4({h4}), EMA({ema[0]}) < Close({Close[0]})");
            }
            // Short entry condition - matching TradingView pine script exactly
            else if (Close[0] < l4 && Open[0] > l4 && ema[0] > Close[0] && Position.MarketPosition == MarketPosition.Flat)
            {
                EnterShort(1, "Short");
                entryPrice = Close[0];
                lowestSinceEntry = Close[0];
                isInShortPosition = true;
                isInLongPosition = false;
                shortTrailingActive = false;
                
                // Log entry
                Print($"SHORT ENTRY: {Instrument.FullName} at {Close[0]}, Time: {Time[0]}");
                Print($"Entry Conditions: Close({Close[0]}) < L4({l4}), Open({Open[0]}) > L4({l4}), EMA({ema[0]}) > Close({Close[0]})");
            }

            // Trailing stop logic
            // Manage trailing stop for long
            if (Position.MarketPosition == MarketPosition.Long && isInLongPosition)
            {
                if (Close[0] > highestSinceEntry)
                    highestSinceEntry = Close[0];

                // CORRECTED: Convert points to ticks using pointSize
                // Activate trailing stop only after price moves LongTrailPoints points in our favor
                double longTrailPointsInTicks = LongTrailPoints * pointSize;
                if (!longTrailingActive && Close[0] >= entryPrice + (longTrailPointsInTicks * TickSize))
                {
                    longTrailingActive = true;
                    Print($"LONG TRAIL ACTIVATED: Price={Close[0]}, Entry={entryPrice}, Points={LongTrailPoints} points ({longTrailPointsInTicks} ticks)");
                }

                if (longTrailingActive)
                {
                    // CORRECTED: Convert points to ticks using pointSize
                    // Set trailing stop LongTrailOffset points behind the highest price
                    double longTrailOffsetInTicks = LongTrailOffset * pointSize;
                    double newStop = highestSinceEntry - (longTrailOffsetInTicks * TickSize);
                    ExitLongStopMarket(0, true, Position.Quantity, newStop, "ExitLongTrail", "Long");
                }

                // Fixed stop loss - convert points to ticks
                double longStopLossInTicks = LongStopLoss * pointSize;
                if (Close[0] <= entryPrice - (longStopLossInTicks * TickSize))
                {
                    ExitLong("ExitLongStop", "Long");
                    Print($"LONG STOP LOSS: Price={Close[0]}, Entry={entryPrice}, Loss={LongStopLoss} points ({longStopLossInTicks} ticks)");
                    isInLongPosition = false;
                }
            }

            // Manage trailing stop for short
            if (Position.MarketPosition == MarketPosition.Short && isInShortPosition)
            {
                if (Close[0] < lowestSinceEntry)
                    lowestSinceEntry = Close[0];

                // CORRECTED: Convert points to ticks using pointSize
                // Activate trailing stop only after price moves ShortTrailPoints points in our favor
                double shortTrailPointsInTicks = ShortTrailPoints * pointSize;
                if (!shortTrailingActive && Close[0] <= entryPrice - (shortTrailPointsInTicks * TickSize))
                {
                    shortTrailingActive = true;
                    Print($"SHORT TRAIL ACTIVATED: Price={Close[0]}, Entry={entryPrice}, Points={ShortTrailPoints} points ({shortTrailPointsInTicks} ticks)");
                }

                if (shortTrailingActive)
                {
                    // CORRECTED: Convert points to ticks using pointSize
                    // Set trailing stop ShortTrailOffset points behind the lowest price
                    double shortTrailOffsetInTicks = ShortTrailOffset * pointSize;
                    double newStop = lowestSinceEntry + (shortTrailOffsetInTicks * TickSize);
                    ExitShortStopMarket(0, true, Position.Quantity, newStop, "ExitShortTrail", "Short");
                }

                // Fixed stop loss - convert points to ticks
                double shortStopLossInTicks = ShortStopLoss * pointSize;
                if (Close[0] >= entryPrice + (shortStopLossInTicks * TickSize))
                {
                    ExitShort("ExitShortStop", "Short");
                    Print($"SHORT STOP LOSS: Price={Close[0]}, Entry={entryPrice}, Loss={ShortStopLoss} points ({shortStopLossInTicks} ticks)");
                    isInShortPosition = false;
                }
            }
            
            // Reset position flags if we're flat
            if (Position.MarketPosition == MarketPosition.Flat)
            {
                isInLongPosition = false;
                isInShortPosition = false;
            }
        }
        
        #region Properties
        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "EMA Period", Description = "Period for the EMA indicator", Order = 1, GroupName = "Parameters")]
        public int EmaPeriod { get; set; }
        
        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "Long Trail Points", Description = "Trail points for long positions (in price points, not ticks)", Order = 2, GroupName = "Parameters")]
        public int LongTrailPoints { get; set; }
        
        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "Long Trail Offset", Description = "Trail offset for long positions (in price points, not ticks)", Order = 3, GroupName = "Parameters")]
        public int LongTrailOffset { get; set; }
        
        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "Long Stop Loss", Description = "Stop loss for long positions (in price points, not ticks)", Order = 4, GroupName = "Parameters")]
        public int LongStopLoss { get; set; }
        
        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "Short Trail Points", Description = "Trail points for short positions (in price points, not ticks)", Order = 5, GroupName = "Parameters")]
        public int ShortTrailPoints { get; set; }
        
        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "Short Trail Offset", Description = "Trail offset for short positions (in price points, not ticks)", Order = 6, GroupName = "Parameters")]
        public int ShortTrailOffset { get; set; }
        
        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "Short Stop Loss", Description = "Stop loss for short positions (in price points, not ticks)", Order = 7, GroupName = "Parameters")]
        public int ShortStopLoss { get; set; }
        #endregion
    }
}
