#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.Indicators;
using NinjaTrader.NinjaScript.DrawingTools;
#endregion

//This namespace holds Strategies in this folder and is required. Do not change it.
namespace NinjaTrader.NinjaScript.Strategies
{
    public class SimpleCamarillaTest : Strategy
    {
        // Variables for Camarilla levels
        private double h4;
        private double l4;

        // EMA indicator
        private EMA ema;

        // Variables for trailing stop
        private double entryPrice;
        private double highestSinceEntry;
        private double lowestSinceEntry;
        private bool longTrailingActive;
        private bool shortTrailingActive;

        protected override void OnStateChange()
        {
            if (State == State.SetDefaults)
            {
                Name = "SimpleCamarillaTest";
                Description = "Simple test strategy for Camarilla levels with online trailing";
                Calculate = Calculate.OnBarClose; // Changed to OnBarClose for testing, change back to OnEachTick for real-time trailing
                EntriesPerDirection = 1;
                EntryHandling = EntryHandling.AllEntries;
                IsExitOnSessionCloseStrategy = true;
                ExitOnSessionCloseSeconds = 30;
                IsFillLimitOnTouch = false;
                MaximumBarsLookBack = MaximumBarsLookBack.TwoHundredFiftySix;
                OrderFillResolution = OrderFillResolution.Standard;
                Slippage = 0;
                StartBehavior = StartBehavior.WaitUntilFlat;
                TimeInForce = TimeInForce.Gtc;
                TraceOrders = true;
                RealtimeErrorHandling = RealtimeErrorHandling.StopCancelClose;
                StopTargetHandling = StopTargetHandling.PerEntryExecution;
                BarsRequiredToTrade = 20;

                // Default trail parameters - matched with Pine Script
                LongTrailPoints = 40;
                LongTrailOffset = 1;
                LongStopLoss = 70;
                ShortTrailPoints = 20;
                ShortTrailOffset = 1;
                ShortStopLoss = 40;

                // Default parameter values
                EmaPeriod = 8;
            }
            else if (State == State.Configure)
            {
                // Add EMA indicator
                ema = EMA(EmaPeriod);
                AddChartIndicator(ema);
            }
        }

        private bool entryEvaluatedThisBar = false;

        protected override void OnBarUpdate()
        {
            try
            {
                // Skip processing if we don't have enough bars - REDUCED TO MINIMUM
                if (BarsInProgress != 0 || CurrentBar < 1) // Only need 1 bar
                    return;

                // Debug info - print every bar
                if (IsFirstTickOfBar)
                {
                    Print($"Processing bar: {CurrentBar}, Time: {Time[0]}, Position: {Position.MarketPosition}");
                    // Reset entry flag at the start of a new bar
                    entryEvaluatedThisBar = false;
                }

                // Get current bar date
                DateTime currentDate = Time[0].Date;

                // Calculate H4/L4 values dynamically from previous day's data
                // We need to find the previous day's OHLC values

                // Initialize with default values
                double prevDayHigh = 0;
                double prevDayLow = 0;
                double prevDayClose = 0;
                bool foundPrevDay = false;

                // Look back up to 10 days to find the previous day's data
                for (int i = 1; i < Math.Min(CurrentBar, 1000); i++)
                {
                    // If we found a bar from a different (previous) day
                    if (Time[i].Date < currentDate)
                    {
                        // Get the OHLC for that day
                        DateTime prevDate = Time[i].Date;
                        prevDayHigh = double.MinValue;
                        prevDayLow = double.MaxValue;
                        prevDayClose = 0;

                        // Find high/low/close for the previous day
                        for (int j = i; j < Math.Min(CurrentBar, i + 1000); j++)
                        {
                            if (Time[j].Date == prevDate)
                            {
                                prevDayHigh = Math.Max(prevDayHigh, High[j]);
                                prevDayLow = Math.Min(prevDayLow, Low[j]);
                                prevDayClose = Close[j]; // This will be overwritten until we get the last bar of the day
                            }
                            else if (Time[j].Date < prevDate)
                            {
                                // We've moved to an even earlier day, stop searching
                                break;
                            }
                        }

                        foundPrevDay = true;
                        break;
                    }
                }

                // Calculate H4/L4 using Camarilla formula
                if (foundPrevDay)
                {
                    // Camarilla formula: H4 = Close + (High - Low) * 1.1 / 2.0
                    h4 = prevDayClose + (prevDayHigh - prevDayLow) * 1.1 / 2.0;
                    // Camarilla formula: L4 = Close - (High - Low) * 1.1 / 2.0
                    l4 = prevDayClose - (prevDayHigh - prevDayLow) * 1.1 / 2.0;

                    Print($"Calculated H4/L4 for {currentDate:yyyy-MM-dd} based on previous day: H4={h4}, L4={l4}, PrevHigh={prevDayHigh}, PrevLow={prevDayLow}, PrevClose={prevDayClose}");
                }
                else
                {
                    // Default values if we can't find previous day's data
                    h4 = 5900;
                    l4 = 5800;
                    Print($"Using default H4/L4 values for {currentDate:yyyy-MM-dd} - could not find previous day's data");
                }

                // Draw H4/L4 levels on first bar of the day or when the date changes
                if (CurrentBar == 0 || Time[1].Date != currentDate)
                {
                    string dateStr = currentDate.ToString("yyyyMMdd");

                    // Remove old lines if they exist
                    RemoveDrawObject("H4_" + dateStr);
                    RemoveDrawObject("L4_" + dateStr);

                    // Draw horizontal lines for H4/L4 with day boundaries
                    // Calculate start and end points for the day
                    DateTime dayStart = Time[0].Date;
                    DateTime dayEnd = dayStart.AddDays(1).AddSeconds(-1);

                    // Draw H4/L4 lines with day boundaries
                    Draw.Line(this, "H4_" + dateStr, false, dayStart, h4, dayEnd, h4, Brushes.Orange, DashStyleHelper.Solid, 2);
                    Draw.Line(this, "L4_" + dateStr, false, dayStart, l4, dayEnd, l4, Brushes.Orange, DashStyleHelper.Solid, 2);

                    // Add text labels
                    Draw.Text(this, "H4_Text_" + dateStr, "H4: " + h4.ToString("0.00"), 0, h4 + 5 * TickSize, Brushes.Orange);
                    Draw.Text(this, "L4_Text_" + dateStr, "L4: " + l4.ToString("0.00"), 0, l4 - 5 * TickSize, Brushes.Orange);
                }

                // CAMARILLA ENTRY CONDITIONS
                // Long entry: Price crosses above H4
                bool longCondition1 = CrossAbove(Close, h4, 1);
                bool longCondition2 = Close[0] > h4;
                bool longCondition3 = Position.MarketPosition == MarketPosition.Flat;

                // Short entry: Price crosses below L4
                bool shortCondition1 = CrossBelow(Close, l4, 1);
                bool shortCondition2 = Close[0] < l4;
                bool shortCondition3 = Position.MarketPosition == MarketPosition.Flat;

                // Entry logic - ONLY EVALUATE ON BAR CLOSE
                // This ensures entries happen like in TradingView (only at bar close)
                if (!entryEvaluatedThisBar && Calculate == Calculate.OnBarClose ||
                    !entryEvaluatedThisBar && Calculate == Calculate.OnEachTick && IsFirstTickOfBar)
                {
                    // Long entry
                    if (longCondition1 && longCondition2 && longCondition3)
                    {
                        EnterLong(1, "Long");
                        entryPrice = Close[0];
                        highestSinceEntry = Close[0];
                        longTrailingActive = false;
                        Draw.ArrowUp(this, "LongEntry_" + CurrentBar, true, 0, Low[0] - 10 * TickSize, Brushes.Green);
                        Draw.Text(this, "LongEntryText_" + CurrentBar, "LONG", 0, Low[0] - 20 * TickSize, Brushes.Green);
                        Print($"LONG ENTRY: {Instrument.FullName} at {Close[0]}, Time: {Time[0]}, Bar: {CurrentBar}");
                    }
                    // Short entry
                    else if (shortCondition1 && shortCondition2 && shortCondition3)
                    {
                        EnterShort(1, "Short");
                        entryPrice = Close[0];
                        lowestSinceEntry = Close[0];
                        shortTrailingActive = false;
                        Draw.ArrowDown(this, "ShortEntry_" + CurrentBar, true, 0, High[0] + 10 * TickSize, Brushes.Red);
                        Draw.Text(this, "ShortEntryText_" + CurrentBar, "SHORT", 0, High[0] + 20 * TickSize, Brushes.Red);
                        Print($"SHORT ENTRY: {Instrument.FullName} at {Close[0]}, Time: {Time[0]}, Bar: {CurrentBar}");
                    }

                    // Mark that we've evaluated entries for this bar
                    entryEvaluatedThisBar = true;
                }

                // Trailing stop logic
                // Manage trailing stop for long
                if (Position.MarketPosition == MarketPosition.Long)
                {
                    // Update highest price since entry
                    if (Close[0] > highestSinceEntry)
                        highestSinceEntry = Close[0];

                    // Activate trailing stop only after price moves LongTrailPoints ticks in our favor
                    if (!longTrailingActive && Close[0] >= entryPrice + (LongTrailPoints * TickSize))
                    {
                        longTrailingActive = true;
                        Print($"LONG TRAIL ACTIVATED: Price={Close[0]}, Entry={entryPrice}, Points={LongTrailPoints} ticks");
                    }

                    // If trailing stop is active, update the stop price
                    if (longTrailingActive)
                    {
                        double newStop = highestSinceEntry - (LongTrailOffset * TickSize);
                        ExitLongStopMarket(0, true, Position.Quantity, newStop, "LongTrail", "Long");

                        // Draw trailing stop line - only update once per bar to reduce clutter
                        if (IsFirstTickOfBar)
                            Draw.Line(this, "LongTrailStop", false, 0, newStop, 10, newStop, Brushes.Blue, DashStyleHelper.Dash, 1);
                    }

                    // Fixed stop loss
                    double stopPrice = entryPrice - (LongStopLoss * TickSize);

                    // Draw stop loss line - only once when position is opened
                    if (IsFirstTickOfBar && BarsSinceEntryExecution() == 0)
                        Draw.Line(this, "LongStopLoss", false, 0, stopPrice, 10, stopPrice, Brushes.Red, DashStyleHelper.Dash, 1);

                    // Check if stop loss hit
                    if (Close[0] <= stopPrice)
                    {
                        ExitLong("LongStopLoss", "Long");
                        Print($"LONG STOP LOSS: Price={Close[0]}, Entry={entryPrice}, Loss={LongStopLoss} ticks");
                    }
                }

                // Manage trailing stop for short
                if (Position.MarketPosition == MarketPosition.Short)
                {
                    // Update lowest price since entry
                    if (Close[0] < lowestSinceEntry)
                        lowestSinceEntry = Close[0];

                    // Activate trailing stop only after price moves ShortTrailPoints ticks in our favor
                    if (!shortTrailingActive && Close[0] <= entryPrice - (ShortTrailPoints * TickSize))
                    {
                        shortTrailingActive = true;
                        Print($"SHORT TRAIL ACTIVATED: Price={Close[0]}, Entry={entryPrice}, Points={ShortTrailPoints} ticks");
                    }

                    // If trailing stop is active, update the stop price
                    if (shortTrailingActive)
                    {
                        double newStop = lowestSinceEntry + (ShortTrailOffset * TickSize);
                        ExitShortStopMarket(0, true, Position.Quantity, newStop, "ShortTrail", "Short");

                        // Draw trailing stop line - only update once per bar to reduce clutter
                        if (IsFirstTickOfBar)
                            Draw.Line(this, "ShortTrailStop", false, 0, newStop, 10, newStop, Brushes.Blue, DashStyleHelper.Dash, 1);
                    }

                    // Fixed stop loss
                    double stopPrice = entryPrice + (ShortStopLoss * TickSize);

                    // Draw stop loss line - only once when position is opened
                    if (IsFirstTickOfBar && BarsSinceEntryExecution() == 0)
                        Draw.Line(this, "ShortStopLoss", false, 0, stopPrice, 10, stopPrice, Brushes.Red, DashStyleHelper.Dash, 1);

                    // Check if stop loss hit
                    if (Close[0] >= stopPrice)
                    {
                        ExitShort("ShortStopLoss", "Short");
                        Print($"SHORT STOP LOSS: Price={Close[0]}, Entry={entryPrice}, Loss={ShortStopLoss} ticks");
                    }
                }
            }
            catch (Exception ex)
            {
                Print($"Error in OnBarUpdate: {ex.Message}");
            }
        }

        #region Properties
        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name="EMA Period", Description="Period for EMA indicator", Order=1, GroupName="Parameters")]
        public int EmaPeriod
        { get; set; }

        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name="Long Trail Points", Description="Trail points for long positions", Order=2, GroupName="Parameters")]
        public int LongTrailPoints
        { get; set; }

        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name="Long Trail Offset", Description="Trail offset for long positions", Order=3, GroupName="Parameters")]
        public int LongTrailOffset
        { get; set; }

        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name="Long Stop Loss", Description="Stop loss for long positions", Order=4, GroupName="Parameters")]
        public int LongStopLoss
        { get; set; }

        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name="Short Trail Points", Description="Trail points for short positions", Order=5, GroupName="Parameters")]
        public int ShortTrailPoints
        { get; set; }

        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name="Short Trail Offset", Description="Trail offset for short positions", Order=6, GroupName="Parameters")]
        public int ShortTrailOffset
        { get; set; }

        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name="Short Stop Loss", Description="Stop loss for short positions", Order=7, GroupName="Parameters")]
        public int ShortStopLoss
        { get; set; }
        #endregion
    }
}
