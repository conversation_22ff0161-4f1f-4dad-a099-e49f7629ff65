#region Using declarations
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Input;
using System.Windows.Media;
using System.Xml.Serialization;
using NinjaTrader.Cbi;
using NinjaTrader.Gui;
using NinjaTrader.Gui.Chart;
using NinjaTrader.Gui.SuperDom;
using NinjaTrader.Gui.Tools;
using NinjaTrader.Data;
using NinjaTrader.NinjaScript;
using NinjaTrader.Core.FloatingPoint;
using NinjaTrader.NinjaScript.Indicators;
using NinjaTrader.NinjaScript.DrawingTools;
#endregion

//This namespace holds Strategies in this folder and is required. Do not change it. 
namespace NinjaTrader.NinjaScript.Strategies
{
    public class CamarillaStrategy : Strategy
    {
        private EMA ema;
        private double h4;
        private double l4;
        private double entryPrice;
        private double highestSinceEntry;
        private double lowestSinceEntry;
        private bool longTrailingActive;
        private bool shortTrailingActive;

        protected override void OnStateChange()
        {
            if (State == State.SetDefaults)
            {
                Name = "CamarillaStrategy";
                Description = "Camarilla Strategy based on H4/L4 levels with custom trailing stop";
                Calculate = Calculate.OnEachTick;
                IsOverlay = true;
                
                // Default parameters - matching TradingView pine script
                EmaPeriod = 8;
                LongTrailPoints = 60;
                LongTrailOffset = 1;
                LongStopLoss = 100;
                ShortTrailPoints = 40;
                ShortTrailOffset = 1;
                ShortStopLoss = 70;
            }
            else if (State == State.Configure)
            {
                AddDataSeries(Data.BarsPeriodType.Day, 1);
            }
            else if (State == State.DataLoaded)
            {
                ema = EMA(EmaPeriod);
            }
        }

        protected override void OnBarUpdate()
        {
            if (BarsInProgress != 0 || CurrentBar < 5 || BarsArray[1].Count < 2)
                return;

            // Daily Camarilla H4/L4 based on previous day
            double dailyHigh = Highs[1][1];
            double dailyLow = Lows[1][1];
            double dailyClose = Closes[1][1];

            h4 = dailyClose + (dailyHigh - dailyLow) * 1.1 / 2.0;
            l4 = dailyClose - (dailyHigh - dailyLow) * 1.1 / 2.0;

            // Long entry condition - FIXED to match TradingView pine script
            if (Close[0] > h4 && Open[0] < h4 && ema[0] < Close[0] && Position.MarketPosition == MarketPosition.Flat)
            {
                EnterLong("Long");
                entryPrice = Close[0];
                highestSinceEntry = Close[0];
                longTrailingActive = false;
                
                // Log entry
                Print($"LONG ENTRY: {Instrument.FullName} at {Close[0]}, Time: {Time[0]}");
            }
            // Short entry condition - FIXED to match TradingView pine script
            else if (Close[0] < l4 && Open[0] > l4 && ema[0] > Close[0] && Position.MarketPosition == MarketPosition.Flat)
            {
                EnterShort("Short");
                entryPrice = Close[0];
                lowestSinceEntry = Close[0];
                shortTrailingActive = false;
                
                // Log entry
                Print($"SHORT ENTRY: {Instrument.FullName} at {Close[0]}, Time: {Time[0]}");
            }

            // Manage trailing stop for long
            if (Position.MarketPosition == MarketPosition.Long)
            {
                if (Close[0] > highestSinceEntry)
                    highestSinceEntry = Close[0];

                // FIXED: Activate trailing stop only after price moves LongTrailOffset ticks in our favor
                if (!longTrailingActive && Close[0] >= entryPrice + (LongTrailOffset * TickSize))
                {
                    longTrailingActive = true;
                    Print($"LONG TRAIL ACTIVATED: Price={Close[0]}, Entry={entryPrice}, Offset={LongTrailOffset} ticks");
                }

                if (longTrailingActive)
                {
                    double newStop = highestSinceEntry - (LongTrailPoints * TickSize);
                    ExitLongStopMarket(0, true, Position.Quantity, newStop, "ExitLongTrail", "Long");
                }

                // Fixed stop loss
                if (Close[0] <= entryPrice - (LongStopLoss * TickSize))
                {
                    ExitLong("ExitLongStop", "Long");
                    Print($"LONG STOP LOSS: Price={Close[0]}, Entry={entryPrice}, Loss={LongStopLoss} ticks");
                }
            }

            // Manage trailing stop for short
            if (Position.MarketPosition == MarketPosition.Short)
            {
                if (Close[0] < lowestSinceEntry)
                    lowestSinceEntry = Close[0];

                // FIXED: Activate trailing stop only after price moves ShortTrailOffset ticks in our favor
                if (!shortTrailingActive && Close[0] <= entryPrice - (ShortTrailOffset * TickSize))
                {
                    shortTrailingActive = true;
                    Print($"SHORT TRAIL ACTIVATED: Price={Close[0]}, Entry={entryPrice}, Offset={ShortTrailOffset} ticks");
                }

                if (shortTrailingActive)
                {
                    double newStop = lowestSinceEntry + (ShortTrailPoints * TickSize);
                    ExitShortStopMarket(0, true, Position.Quantity, newStop, "ExitShortTrail", "Short");
                }

                // Fixed stop loss
                if (Close[0] >= entryPrice + (ShortStopLoss * TickSize))
                {
                    ExitShort("ExitShortStop", "Short");
                    Print($"SHORT STOP LOSS: Price={Close[0]}, Entry={entryPrice}, Loss={ShortStopLoss} ticks");
                }
            }
        }
        
        #region Properties
        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "EMA Period", Description = "Period for the EMA indicator", Order = 1, GroupName = "Parameters")]
        public int EmaPeriod { get; set; }
        
        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "Long Trail Points", Description = "Trail points for long positions", Order = 2, GroupName = "Parameters")]
        public int LongTrailPoints { get; set; }
        
        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "Long Trail Offset", Description = "Trail offset for long positions", Order = 3, GroupName = "Parameters")]
        public int LongTrailOffset { get; set; }
        
        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "Long Stop Loss", Description = "Stop loss for long positions", Order = 4, GroupName = "Parameters")]
        public int LongStopLoss { get; set; }
        
        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "Short Trail Points", Description = "Trail points for short positions", Order = 5, GroupName = "Parameters")]
        public int ShortTrailPoints { get; set; }
        
        [NinjaScriptProperty]
        [Range(0, int.MaxValue)]
        [Display(Name = "Short Trail Offset", Description = "Trail offset for short positions", Order = 6, GroupName = "Parameters")]
        public int ShortTrailOffset { get; set; }
        
        [NinjaScriptProperty]
        [Range(1, int.MaxValue)]
        [Display(Name = "Short Stop Loss", Description = "Stop loss for short positions", Order = 7, GroupName = "Parameters")]
        public int ShortStopLoss { get; set; }
        #endregion
    }
}
